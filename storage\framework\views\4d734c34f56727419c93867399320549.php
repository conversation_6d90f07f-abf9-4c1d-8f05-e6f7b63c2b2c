<?php $__env->startSection('title', 'Sliders & Banners'); ?>
<?php $__env->startSection('page-title', 'Sliders & Banners Management'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.cms.sliders.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add Slider
        </a>
        <button type="button" class="btn btn-outline-info" onclick="toggleSortMode()">
            <i class="fas fa-sort me-1"></i> Reorder
        </button>
        <a href="<?php echo e(route('admin.cms.sliders.preview')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-eye me-1"></i> Preview
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Sort Mode Instructions -->
    <div id="sortInstructions" class="alert alert-info" style="display: none;">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Sort Mode Active:</strong> Drag and drop slider cards to reorder them. Click "Save Order" when finished.
        <button type="button" class="btn btn-sm btn-success ms-3" onclick="saveOrder()">
            <i class="fas fa-save me-1"></i> Save Order
        </button>
        <button type="button" class="btn btn-sm btn-secondary ms-2" onclick="cancelSort()">
            <i class="fas fa-times me-1"></i> Cancel
        </button>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                Sliders & Banners (<?php echo e($sliders->total()); ?> total)
            </h6>
            <div class="text-muted small">
                Showing <?php echo e($sliders->firstItem() ?? 0); ?> to <?php echo e($sliders->lastItem() ?? 0); ?> of <?php echo e($sliders->total()); ?> results
            </div>
        </div>
        <div class="card-body">
            <?php if($sliders->count() > 0): ?>
                <div id="sortable-container">
                    <div class="row" id="sliders-grid">
                        <?php $__currentLoopData = $sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-6 col-xl-4 mb-4" data-id="<?php echo e($slider->id); ?>">
                                <div class="card slider-card h-100">
                                    <div class="position-relative">
                                        <?php if($slider->image): ?>
                                            <img src="<?php echo e(Storage::url($slider->image)); ?>" 
                                                 alt="<?php echo e($slider->title); ?>" 
                                                 class="card-img-top" 
                                                 style="height: 200px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                                 style="height: 200px;">
                                                <i class="fas fa-image fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Status Badge -->
                                        <span class="position-absolute top-0 start-0 m-2 badge bg-<?php echo e($slider->is_active ? 'success' : 'secondary'); ?>">
                                            <?php echo e($slider->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                        
                                        <!-- Order Badge -->
                                        <span class="position-absolute top-0 end-0 m-2 badge bg-primary">
                                            #<?php echo e($slider->display_order); ?>

                                        </span>
                                        
                                        <!-- Actions Dropdown -->
                                        <div class="position-absolute bottom-0 end-0 m-2">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-dark dropdown-toggle" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="<?php echo e(route('admin.cms.sliders.show', $slider)); ?>">
                                                        <i class="fas fa-eye me-2"></i> View
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="<?php echo e(route('admin.cms.sliders.edit', $slider)); ?>">
                                                        <i class="fas fa-edit me-2"></i> Edit
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" 
                                                           onclick="duplicateSlider('<?php echo e($slider->id); ?>', '<?php echo e($slider->title); ?>')">
                                                        <i class="fas fa-copy me-2"></i> Duplicate
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item" href="#" 
                                                           onclick="toggleStatus('<?php echo e($slider->id); ?>', '<?php echo e($slider->title); ?>', <?php echo e($slider->is_active ? 'false' : 'true'); ?>)">
                                                        <i class="fas fa-<?php echo e($slider->is_active ? 'eye-slash' : 'eye'); ?> me-2"></i> 
                                                        <?php echo e($slider->is_active ? 'Deactivate' : 'Activate'); ?>

                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="#" 
                                                           onclick="confirmDelete('<?php echo e($slider->title); ?>', '<?php echo e(route('admin.cms.sliders.destroy', $slider)); ?>')">
                                                        <i class="fas fa-trash me-2"></i> Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="card-body">
                                        <h6 class="card-title mb-2"><?php echo e($slider->title); ?></h6>
                                        
                                        <?php if($slider->subtitle): ?>
                                            <p class="text-muted small mb-2"><?php echo e($slider->subtitle); ?></p>
                                        <?php endif; ?>
                                        
                                        <?php if($slider->description): ?>
                                            <p class="card-text small"><?php echo e(Str::limit($slider->description, 100)); ?></p>
                                        <?php endif; ?>
                                        
                                        <?php if($slider->button_text && $slider->button_url): ?>
                                            <div class="mb-2">
                                                <span class="badge bg-info">
                                                    <i class="fas fa-link me-1"></i> <?php echo e($slider->button_text); ?>

                                                </span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                Created <?php echo e($slider->created_at->format('M d, Y')); ?>

                                            </small>
                                            <?php if($slider->mobile_image): ?>
                                                <span class="badge bg-success" title="Has mobile version">
                                                    <i class="fas fa-mobile-alt"></i>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    <?php echo e($sliders->links('pagination.admin')); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-images fa-4x text-muted mb-4"></i>
                    <h5 class="text-muted">No Sliders Found</h5>
                    <p class="text-muted">Get started by creating your first slider or banner.</p>
                    <a href="<?php echo e(route('admin.cms.sliders.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Create First Slider
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the slider <strong id="deleteSliderTitle"></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone and will delete all associated images.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">Delete Slider</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Slider Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="sliderPreview" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner" id="previewContent">
                            <!-- Content will be loaded dynamically -->
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#sliderPreview" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#sliderPreview" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .slider-card {
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    .slider-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    .sortable-mode .slider-card {
        cursor: move;
        border-color: #007bff;
    }
    .sortable-mode .slider-card:hover {
        border-color: #0056b3;
    }
    .ui-sortable-helper {
        transform: rotate(5deg);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.3);
    }
    .carousel-item img {
        height: 400px;
        object-fit: cover;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
<script>
    let sortMode = false;
    let originalOrder = [];

    function confirmDelete(sliderTitle, deleteUrl) {
        document.getElementById('deleteSliderTitle').textContent = sliderTitle;
        document.getElementById('deleteForm').action = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function toggleStatus(sliderId, sliderTitle, newStatus) {
        if (confirm(`Are you sure you want to ${newStatus === 'true' ? 'activate' : 'deactivate'} "${sliderTitle}"?`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/cms/sliders/${sliderId}/toggle-status`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);

            document.body.appendChild(form);
            form.submit();
        }
    }

    function duplicateSlider(sliderId, sliderTitle) {
        if (confirm(`Are you sure you want to duplicate "${sliderTitle}"?`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/cms/sliders/${sliderId}/duplicate`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);

            document.body.appendChild(form);
            form.submit();
        }
    }

    function toggleSortMode() {
        sortMode = !sortMode;
        const container = document.getElementById('sortable-container');
        const instructions = document.getElementById('sortInstructions');
        
        if (sortMode) {
            // Store original order
            originalOrder = Array.from(document.querySelectorAll('[data-id]')).map(el => el.dataset.id);
            
            // Enable sorting
            container.classList.add('sortable-mode');
            instructions.style.display = 'block';
            
            // Initialize jQuery UI sortable
            $('#sliders-grid').sortable({
                items: '.col-lg-6',
                placeholder: 'ui-state-highlight',
                helper: 'clone',
                opacity: 0.8
            });
        } else {
            cancelSort();
        }
    }

    function saveOrder() {
        const newOrder = Array.from(document.querySelectorAll('[data-id]')).map(el => el.dataset.id);
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/cms/sliders/update-order';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);
        
        newOrder.forEach((id, index) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `orders[${index}]`;
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }

    function cancelSort() {
        sortMode = false;
        const container = document.getElementById('sortable-container');
        const instructions = document.getElementById('sortInstructions');
        
        container.classList.remove('sortable-mode');
        instructions.style.display = 'none';
        
        // Destroy sortable
        if ($('#sliders-grid').hasClass('ui-sortable')) {
            $('#sliders-grid').sortable('destroy');
        }
        
        // Restore original order if needed
        if (originalOrder.length > 0) {
            const grid = document.getElementById('sliders-grid');
            const items = Array.from(grid.children);
            
            originalOrder.forEach(id => {
                const item = items.find(el => el.dataset.id === id);
                if (item) {
                    grid.appendChild(item);
                }
            });
        }
    }

    function previewSliders() {
        // Redirect to the new preview page
        window.location.href = '<?php echo e(route("admin.cms.sliders.preview")); ?>';
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/sliders/index.blade.php ENDPATH**/ ?>