<?php $__env->startSection('title', 'E-commerce Reports'); ?>
<?php $__env->startSection('page-title', 'E-commerce Reports & Analytics'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary" onclick="refreshReports()">
            <i class="fas fa-sync me-1"></i> Refresh Data
        </button>
        <button type="button" class="btn btn-outline-success" onclick="exportData()">
            <i class="fas fa-download me-1"></i> Export Data
        </button>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Overview Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Products
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e(number_format($overviewStats['total_products'])); ?>

                            </div>
                            <small class="text-muted"><?php echo e($overviewStats['active_products']); ?> active</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo \App\Helpers\CurrencyHelper::format($overviewStats['total_revenue']); ?>
                            </div>
                            <small class="text-muted"><?php echo e($overviewStats['paid_orders']); ?> paid orders</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Categories
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e(number_format($overviewStats['total_categories'])); ?>

                            </div>
                            <small class="text-muted"><?php echo e($overviewStats['active_categories']); ?> active</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-folder fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Stock Alerts
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e($overviewStats['low_stock_alerts'] + $overviewStats['out_of_stock_alerts']); ?>

                            </div>
                            <small class="text-muted"><?php echo e($overviewStats['low_stock_alerts']); ?> low, <?php echo e($overviewStats['out_of_stock_alerts']); ?> out</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Period Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-0">Analytics Period</h6>
                            <small class="text-muted">Select the time period for analytics data</small>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group w-100" role="group" id="periodSelector">
                                <input type="radio" class="btn-check" name="period" id="period7" value="7">
                                <label class="btn btn-outline-primary" for="period7">7 Days</label>

                                <input type="radio" class="btn-check" name="period" id="period30" value="30" checked>
                                <label class="btn btn-outline-primary" for="period30">30 Days</label>

                                <input type="radio" class="btn-check" name="period" id="period90" value="90">
                                <label class="btn btn-outline-primary" for="period90">90 Days</label>

                                <input type="radio" class="btn-check" name="period" id="period365" value="365">
                                <label class="btn btn-outline-primary" for="period365">1 Year</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row mb-4">
        <!-- Product Trend Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Product Creation Trend</h6>
                </div>
                <div class="card-body">
                    <canvas id="productTrendChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Product Status Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Product Status</h6>
                </div>
                <div class="card-body">
                    <canvas id="productStatusChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row mb-4">
        <!-- Sales Trend Chart -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Sales Trend</h6>
                </div>
                <div class="card-body">
                    <canvas id="salesTrendChart" height="150"></canvas>
                </div>
            </div>
        </div>

        <!-- Stock Distribution -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Stock Distribution</h6>
                </div>
                <div class="card-body">
                    <canvas id="stockDistributionChart" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Row -->
    <div class="row mb-4">
        <!-- Category Performance -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Category Performance</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm" id="categoryPerformanceTable">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Products</th>
                                    <th>Avg Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Top Customers</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm" id="topCustomersTable">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Orders</th>
                                    <th>Total Spent</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <!-- Recent Products -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Products</h6>
                </div>
                <div class="card-body">
                    <?php $__currentLoopData = $recentActivity['recent_products']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-0"><?php echo e($product->name); ?></h6>
                                <small class="text-muted"><?php echo \App\Helpers\CurrencyHelper::format($product->price); ?></small>
                            </div>
                            <span class="badge bg-<?php echo e($product->is_active ? 'success' : 'secondary'); ?>">
                                <?php echo e($product->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Orders</h6>
                </div>
                <div class="card-body">
                    <?php $__currentLoopData = $recentActivity['recent_orders']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-0">#<?php echo e($order->order_number); ?></h6>
                                <small class="text-muted"><?php echo e($order->customer->name ?? 'Guest'); ?></small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold"><?php echo \App\Helpers\CurrencyHelper::format($order->total_amount); ?></div>
                                <span class="badge bg-<?php echo e($order->payment_status === 'paid' ? 'success' : 'warning'); ?>">
                                    <?php echo e(ucfirst($order->payment_status)); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Recent Categories -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Categories</h6>
                </div>
                <div class="card-body">
                    <?php $__currentLoopData = $recentActivity['recent_categories']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-0"><?php echo e($category->name); ?></h6>
                                <small class="text-muted"><?php echo e($category->products_count ?? 0); ?> products</small>
                            </div>
                            <span class="badge bg-<?php echo e($category->is_active ? 'success' : 'secondary'); ?>">
                                <?php echo e($category->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal fade" id="exportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Export E-commerce Data</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label">Export Type</label>
                            <select class="form-select" id="exportType">
                                <option value="analytics">Complete Analytics Report</option>
                                <option value="products">Products Data</option>
                                <option value="orders">Orders Data</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">Format</label>
                            <select class="form-select" id="exportFormat">
                                <option value="xlsx">Excel (.xlsx)</option>
                                <option value="csv">CSV (.csv)</option>
                                <option value="pdf">PDF (.pdf)</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">Period</label>
                            <select class="form-select" id="exportPeriod">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="365">Last Year</option>
                            </select>
                        </div>

                        <!-- Filters for Products Export -->
                        <div id="productFilters" style="display: none;">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Category</label>
                                <select class="form-select" id="exportCategoryId">
                                    <option value="">All Categories</option>
                                    <?php $__currentLoopData = \App\Models\Category::where('is_active', true)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Status</label>
                                <select class="form-select" id="exportProductStatus">
                                    <option value="">All Statuses</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Stock Status</label>
                                <select class="form-select" id="exportStockStatus">
                                    <option value="">All Stock Levels</option>
                                    <option value="in_stock">In Stock</option>
                                    <option value="low_stock">Low Stock</option>
                                    <option value="out_of_stock">Out of Stock</option>
                                </select>
                            </div>
                        </div>

                        <!-- Filters for Orders Export -->
                        <div id="orderFilters" style="display: none;">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Order Status</label>
                                <select class="form-select" id="exportOrderStatus">
                                    <option value="">All Statuses</option>
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Payment Status</label>
                                <select class="form-select" id="exportPaymentStatus">
                                    <option value="">All Payment Statuses</option>
                                    <option value="pending">Pending</option>
                                    <option value="paid">Paid</option>
                                    <option value="failed">Failed</option>
                                    <option value="refunded">Refunded</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">Date From</label>
                            <input type="date" class="form-control" id="exportDateFrom">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" id="exportDateTo">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="performExport()">
                        <i class="fas fa-download me-1"></i> Export Data
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .text-gray-800 {
        color: #5a5c69 !important;
    }
    .text-gray-300 {
        color: #dddfeb !important;
    }
    .font-weight-bold {
        font-weight: 700 !important;
    }
    .text-xs {
        font-size: 0.7rem;
    }
    .shadow {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    .h-100 {
        height: 100% !important;
    }
    .py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }
    .no-gutters {
        margin-right: 0;
        margin-left: 0;
    }
    .no-gutters > .col,
    .no-gutters > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    let productTrendChart, productStatusChart, salesTrendChart, stockDistributionChart;

    // Global currency settings
    window.currencySettings = <?php echo json_encode($currencySettings, 15, 512) ?>;

    // Global currency formatting function
    function formatCurrency(amount) {
        const settings = window.currencySettings;
        const symbol = settings.symbol || '$';
        const position = settings.position || 'before';
        const decimals = settings.decimal_places || 2;
        const thousandsSep = settings.thousands_separator || ',';
        const decimalSep = settings.decimal_separator || '.';

        // Format the number
        const formattedAmount = parseFloat(amount || 0).toFixed(decimals);
        const parts = formattedAmount.split('.');
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSep);
        const finalAmount = parts.join(decimalSep);

        // Return with currency symbol in correct position
        return position === 'before' ? symbol + finalAmount : finalAmount + symbol;
    }

    // Initialize charts on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadProductAnalytics();
        loadSalesAnalytics();
        loadInventoryAnalytics();

        // Period selector change event
        document.querySelectorAll('input[name="period"]').forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    loadProductAnalytics();
                    loadSalesAnalytics();
                }
            });
        });
    });

    function getSelectedPeriod() {
        return document.querySelector('input[name="period"]:checked').value;
    }

    function loadProductAnalytics() {
        const period = getSelectedPeriod();

        fetch(`<?php echo e(route('admin.ecommerce.reports.product-analytics')); ?>?period=${period}`)
            .then(response => response.json())
            .then(data => {
                updateProductTrendChart(data.product_trend);
                updateProductStatusChart(data.status_distribution);
                updateCategoryPerformanceTable(data.category_performance);
            })
            .catch(error => {
                console.error('Error loading product analytics:', error);
            });
    }

    function loadSalesAnalytics() {
        const period = getSelectedPeriod();

        fetch(`<?php echo e(route('admin.ecommerce.reports.sales-analytics')); ?>?period=${period}`)
            .then(response => response.json())
            .then(data => {
                updateSalesTrendChart(data.sales_trend);
                updateTopCustomersTable(data.top_customers);
            })
            .catch(error => {
                console.error('Error loading sales analytics:', error);
            });
    }

    function loadInventoryAnalytics() {
        fetch(`<?php echo e(route('admin.ecommerce.reports.inventory-analytics')); ?>`)
            .then(response => response.json())
            .then(data => {
                updateStockDistributionChart(data.inventory_stats);
            })
            .catch(error => {
                console.error('Error loading inventory analytics:', error);
            });
    }

    function updateProductTrendChart(data) {
        const ctx = document.getElementById('productTrendChart').getContext('2d');

        if (productTrendChart) {
            productTrendChart.destroy();
        }

        const labels = Object.keys(data);
        const values = Object.values(data);

        productTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Products Created',
                    data: values,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    function updateProductStatusChart(data) {
        const ctx = document.getElementById('productStatusChart').getContext('2d');

        if (productStatusChart) {
            productStatusChart.destroy();
        }

        const labels = Object.keys(data).map(status => status.replace('_', ' ').toUpperCase());
        const values = Object.values(data);

        productStatusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: [
                        '#28a745', '#dc3545', '#ffc107'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    function updateSalesTrendChart(data) {
        const ctx = document.getElementById('salesTrendChart').getContext('2d');

        if (salesTrendChart) {
            salesTrendChart.destroy();
        }

        const labels = Object.keys(data);
        const revenueData = labels.map(date => data[date].revenue || 0);
        const ordersData = labels.map(date => data[date].orders || 0);

        salesTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Revenue (' + window.currencySettings.code + ')',
                    data: revenueData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    yAxisID: 'y',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Orders',
                    data: ordersData,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    yAxisID: 'y1',
                    tension: 0.4,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    function updateStockDistributionChart(data) {
        const ctx = document.getElementById('stockDistributionChart').getContext('2d');

        if (stockDistributionChart) {
            stockDistributionChart.destroy();
        }

        stockDistributionChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['In Stock', 'Low Stock', 'Out of Stock'],
                datasets: [{
                    label: 'Products',
                    data: [
                        data.total_products - data.low_stock_count - data.out_of_stock_count,
                        data.low_stock_count,
                        data.out_of_stock_count
                    ],
                    backgroundColor: [
                        '#28a745', // In Stock - Green
                        '#ffc107', // Low Stock - Yellow
                        '#dc3545'  // Out of Stock - Red
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    function updateCategoryPerformanceTable(data) {
        const tbody = document.querySelector('#categoryPerformanceTable tbody');
        tbody.innerHTML = '';

        data.forEach(category => {
            const row = tbody.insertRow();
            row.innerHTML = `
                <td><strong>${category.name}</strong></td>
                <td><span class="badge bg-primary">${category.products_count}</span></td>
                <td>${formatCurrency(category.avg_price)}</td>
            `;
        });
    }

    function updateTopCustomersTable(data) {
        const tbody = document.querySelector('#topCustomersTable tbody');
        tbody.innerHTML = '';

        data.forEach(customer => {
            const row = tbody.insertRow();
            row.innerHTML = `
                <td><strong>${customer.name}</strong></td>
                <td><span class="badge bg-info">${customer.orders_count}</span></td>
                <td><strong>${formatCurrency(customer.total_spent)}</strong></td>
            `;
        });
    }

    function refreshReports() {
        loadProductAnalytics();
        loadSalesAnalytics();
        loadInventoryAnalytics();
    }

    function exportData() {
        // Show the export modal
        const exportModal = new bootstrap.Modal(document.getElementById('exportModal'));
        exportModal.show();
    }

    // Handle export type change to show/hide relevant filters
    document.addEventListener('DOMContentLoaded', function() {
        const exportType = document.getElementById('exportType');
        const productFilters = document.getElementById('productFilters');
        const orderFilters = document.getElementById('orderFilters');
        const exportFormat = document.getElementById('exportFormat');

        exportType.addEventListener('change', function() {
            productFilters.style.display = this.value === 'products' ? 'block' : 'none';
            orderFilters.style.display = this.value === 'orders' ? 'block' : 'none';

            // Update format options based on export type
            if (this.value === 'analytics') {
                exportFormat.innerHTML = `
                    <option value="xlsx">Excel (.xlsx)</option>
                    <option value="pdf">PDF (.pdf)</option>
                `;
            } else {
                exportFormat.innerHTML = `
                    <option value="xlsx">Excel (.xlsx)</option>
                    <option value="csv">CSV (.csv)</option>
                `;
            }
        });
    });

    function performExport() {
        const exportType = document.getElementById('exportType').value;
        const format = document.getElementById('exportFormat').value;
        const period = document.getElementById('exportPeriod').value;
        const dateFrom = document.getElementById('exportDateFrom').value;
        const dateTo = document.getElementById('exportDateTo').value;

        // Build the export URL based on type
        let exportUrl = '';
        let params = new URLSearchParams();

        // Add common parameters
        params.append('format', format);
        params.append('period', period);
        if (dateFrom) params.append('date_from', dateFrom);
        if (dateTo) params.append('date_to', dateTo);

        switch (exportType) {
            case 'products':
                exportUrl = '<?php echo e(route("admin.ecommerce.reports.export-products")); ?>';

                // Add product-specific filters
                const categoryId = document.getElementById('exportCategoryId').value;
                const productStatus = document.getElementById('exportProductStatus').value;
                const stockStatus = document.getElementById('exportStockStatus').value;

                if (categoryId) params.append('category_id', categoryId);
                if (productStatus) params.append('status', productStatus);
                if (stockStatus) params.append('stock_status', stockStatus);
                break;

            case 'orders':
                exportUrl = '<?php echo e(route("admin.ecommerce.reports.export-orders")); ?>';

                // Add order-specific filters
                const orderStatus = document.getElementById('exportOrderStatus').value;
                const paymentStatus = document.getElementById('exportPaymentStatus').value;

                if (orderStatus) params.append('status', orderStatus);
                if (paymentStatus) params.append('payment_status', paymentStatus);
                break;

            case 'analytics':
                if (format === 'pdf') {
                    exportUrl = '<?php echo e(route("admin.ecommerce.reports.export-analytics-pdf")); ?>';
                } else {
                    exportUrl = '<?php echo e(route("admin.ecommerce.reports.export-analytics")); ?>';
                }
                break;
        }

        // Perform the export
        const finalUrl = exportUrl + '?' + params.toString();

        // Show loading state
        const exportButton = document.querySelector('#exportModal .btn-primary');
        const originalText = exportButton.innerHTML;
        exportButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Exporting...';
        exportButton.disabled = true;

        // Create a temporary link to trigger download
        const link = document.createElement('a');
        link.href = finalUrl;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Reset button state after a delay
        setTimeout(() => {
            exportButton.innerHTML = originalText;
            exportButton.disabled = false;

            // Close the modal
            const exportModal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
            exportModal.hide();
        }, 2000);
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/ecommerce/reports/index.blade.php ENDPATH**/ ?>