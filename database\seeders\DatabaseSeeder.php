<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            CarrierSeeder::class,
            SiteSettingSeeder::class,
            AdminUserSeeder::class,
            ShippingContainerSeeder::class,
            SparePartsSeeder::class,
            BoxesPackagingSeeder::class,
            SteelProductsSeeder::class,
            SampleParcelSeeder::class,
            // Blog Seeders
            LogisticsTrendsSeeder::class,
            ShippingTechnologySeeder::class,
            SupplyChainSeeder::class,
            FreightForwardingSeeder::class,
            SustainabilitySeeder::class,
        ]);
    }
}
