<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('parcels', function (Blueprint $table) {
            // Insurance fields
            $table->boolean('requires_insurance')->default(false)->after('insurance_cost');
            $table->boolean('insurance_paid')->default(false)->after('requires_insurance');
            $table->decimal('insurance_refund_amount', 10, 2)->nullable()->after('insurance_paid');
            $table->timestamp('insurance_refunded_at')->nullable()->after('insurance_refund_amount');
            $table->text('insurance_notes')->nullable()->after('insurance_refunded_at');
            
            // Diplomatic envelope fields
            $table->boolean('has_diplomatic_envelope')->default(false)->after('insurance_notes');
            $table->decimal('diplomatic_envelope_cost', 10, 2)->nullable()->after('has_diplomatic_envelope');
            $table->text('diplomatic_envelope_details')->nullable()->after('diplomatic_envelope_cost');
            
            // Special services and alerts
            $table->json('special_services')->nullable()->after('diplomatic_envelope_details');
            $table->json('customer_alerts')->nullable()->after('special_services');
            $table->boolean('customer_notified_insurance')->default(false)->after('customer_alerts');
            $table->boolean('customer_notified_diplomatic')->default(false)->after('customer_notified_insurance');
            
            // Add indexes
            $table->index('requires_insurance');
            $table->index('has_diplomatic_envelope');
            $table->index(['customer_notified_insurance', 'requires_insurance']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('parcels', function (Blueprint $table) {
            $table->dropIndex(['customer_notified_insurance', 'requires_insurance']);
            $table->dropIndex(['has_diplomatic_envelope']);
            $table->dropIndex(['requires_insurance']);
            
            $table->dropColumn([
                'requires_insurance',
                'insurance_paid',
                'insurance_refund_amount',
                'insurance_refunded_at',
                'insurance_notes',
                'has_diplomatic_envelope',
                'diplomatic_envelope_cost',
                'diplomatic_envelope_details',
                'special_services',
                'customer_alerts',
                'customer_notified_insurance',
                'customer_notified_diplomatic'
            ]);
        });
    }
};
