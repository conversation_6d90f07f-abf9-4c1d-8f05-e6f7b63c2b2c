<?php $__env->startSection('title', 'Edit Slider'); ?>
<?php $__env->startSection('page-title', 'Edit Slider: ' . $slider->title); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.cms.sliders.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Sliders
        </a>
        <a href="<?php echo e(route('admin.cms.sliders.show', $slider)); ?>" class="btn btn-outline-info">
            <i class="fas fa-eye me-1"></i> View Details
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="<?php echo e(route('admin.cms.sliders.update', $slider)); ?>" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Slider Content</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="title" name="title" value="<?php echo e(old('title', $slider->title)); ?>" required>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="subtitle" name="subtitle" value="<?php echo e(old('subtitle', $slider->subtitle)); ?>">
                            <?php $__errorArgs = ['subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="description" name="description" rows="4" 
                                      placeholder="Brief description for the slider..."><?php echo e(old('description', $slider->description)); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <small class="text-muted">Maximum 1000 characters</small>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Call-to-Action Button</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="button_text" class="form-label">Button Text</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="button_text" name="button_text" value="<?php echo e(old('button_text', $slider->button_text)); ?>"
                                       placeholder="e.g., Get Started, Learn More">
                                <?php $__errorArgs = ['button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="button_url" class="form-label">Button URL</label>
                                <input type="url" class="form-control <?php $__errorArgs = ['button_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="button_url" name="button_url" value="<?php echo e(old('button_url', $slider->button_url)); ?>"
                                       placeholder="https://example.com or /page">
                                <?php $__errorArgs = ['button_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <small class="text-muted">Leave both fields empty if you don't want a button</small>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Display Settings</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="display_order" class="form-label">Display Order</label>
                                <input type="number" class="form-control <?php $__errorArgs = ['display_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="display_order" name="display_order" value="<?php echo e(old('display_order', $slider->display_order)); ?>" 
                                       min="0" step="1">
                                <?php $__errorArgs = ['display_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" <?php echo e(old('is_active', $slider->is_active) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Active Status</strong>
                                        <br><small class="text-muted">Show this slider on the website</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <a href="<?php echo e(route('admin.cms.sliders.show', $slider)); ?>" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times me-1"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Update Slider
                    </button>
                </div>
            </form>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Desktop Image</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div id="desktop-preview" class="mb-3">
                            <?php if($slider->image): ?>
                                <img src="<?php echo e(Storage::url($slider->image)); ?>" 
                                     alt="<?php echo e($slider->title); ?>" 
                                     class="img-fluid mx-auto" 
                                     style="width: 100%; height: 200px; object-fit: cover;" id="current-desktop">
                                <div class="text-muted small mt-2">Current: <?php echo e(basename($slider->image)); ?></div>
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center mx-auto" 
                                     style="width: 100%; height: 200px; border: 2px dashed #ddd;" id="default-desktop">
                                    <div class="text-center">
                                        <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                        <div class="text-muted">Desktop Image Preview</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <img id="desktop-preview-image" src="#" alt="Desktop Preview" 
                                 class="img-fluid mx-auto" 
                                 style="width: 100%; height: 200px; object-fit: cover; display: none;">
                        </div>
                        
                        <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="image" name="image" accept="image/*" onchange="previewImage(this, 'desktop')">
                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <small class="text-muted d-block mt-2">
                            Leave empty to keep current image<br>
                            Supported formats: JPG, PNG, GIF, WebP<br>
                            Maximum size: 5MB<br>
                            Recommended: 1920x800px
                        </small>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Mobile Image (Optional)</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div id="mobile-preview" class="mb-3">
                            <?php if($slider->mobile_image): ?>
                                <img src="<?php echo e(Storage::url($slider->mobile_image)); ?>" 
                                     alt="<?php echo e($slider->title); ?> (Mobile)" 
                                     class="img-fluid mx-auto" 
                                     style="width: 200px; height: 300px; object-fit: cover;" id="current-mobile">
                                <div class="text-muted small mt-2">Current: <?php echo e(basename($slider->mobile_image)); ?></div>
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center mx-auto" 
                                     style="width: 200px; height: 300px; border: 2px dashed #ddd;" id="default-mobile">
                                    <div class="text-center">
                                        <i class="fas fa-mobile-alt fa-3x text-muted mb-2"></i>
                                        <div class="text-muted small">Mobile Image Preview</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <img id="mobile-preview-image" src="#" alt="Mobile Preview" 
                                 class="img-fluid mx-auto" 
                                 style="width: 200px; height: 300px; object-fit: cover; display: none;">
                        </div>
                        
                        <input type="file" class="form-control <?php $__errorArgs = ['mobile_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="mobile_image" name="mobile_image" accept="image/*" onchange="previewImage(this, 'mobile')">
                        <?php $__errorArgs = ['mobile_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <small class="text-muted d-block mt-2">
                            Leave empty to keep current image<br>
                            Supported formats: JPG, PNG, GIF, WebP<br>
                            Maximum size: 5MB<br>
                            Recommended: 768x1024px
                        </small>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Slider Info</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Created:</strong>
                        <div class="text-muted"><?php echo e($slider->created_at->format('M d, Y h:i A')); ?></div>
                    </div>
                    <div class="mb-2">
                        <strong>Last Updated:</strong>
                        <div class="text-muted"><?php echo e($slider->updated_at->format('M d, Y h:i A')); ?></div>
                    </div>
                    <div class="mb-2">
                        <strong>Current Status:</strong>
                        <div>
                            <span class="badge bg-<?php echo e($slider->is_active ? 'success' : 'secondary'); ?>">
                                <?php echo e($slider->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>
                    </div>
                    <div class="mb-2">
                        <strong>Display Order:</strong>
                        <div class="text-muted"><?php echo e($slider->display_order); ?></div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Use high-quality images for best visual impact</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Keep text concise and action-oriented</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Mobile images ensure optimal mobile experience</small>
                        </li>
                        <li>
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Use display order to control slider sequence</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function previewImage(input, type) {
        const preview = document.getElementById(`${type}-preview-image`);
        const currentImage = document.getElementById(`current-${type}`);
        const defaultPreview = document.getElementById(`default-${type}`);
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
                if (currentImage) currentImage.style.display = 'none';
                if (defaultPreview) defaultPreview.style.display = 'none';
            };
            
            reader.readAsDataURL(input.files[0]);
        } else {
            preview.style.display = 'none';
            if (currentImage) {
                currentImage.style.display = 'block';
            } else if (defaultPreview) {
                defaultPreview.style.display = 'flex';
            }
        }
    }

    // Character counter for description
    document.getElementById('description').addEventListener('input', function() {
        const maxLength = 1000;
        const currentLength = this.value.length;
        const remaining = maxLength - currentLength;
        
        // Find or create counter element
        let counter = document.getElementById('description-counter');
        if (!counter) {
            counter = document.createElement('small');
            counter.id = 'description-counter';
            counter.className = 'text-muted';
            this.parentNode.appendChild(counter);
        }
        
        counter.textContent = `${currentLength}/${maxLength} characters`;
        counter.className = remaining < 100 ? 'text-warning' : (remaining < 50 ? 'text-danger' : 'text-muted');
    });

    // Auto-fill button URL based on button text
    document.getElementById('button_text').addEventListener('input', function() {
        const buttonUrl = document.getElementById('button_url');
        if (!buttonUrl.value && this.value) {
            const text = this.value.toLowerCase();
            if (text.includes('quote') || text.includes('get started')) {
                buttonUrl.value = '/quote';
            } else if (text.includes('track')) {
                buttonUrl.value = '/track';
            } else if (text.includes('about') || text.includes('learn more')) {
                buttonUrl.value = '/about';
            } else if (text.includes('contact')) {
                buttonUrl.value = '/contact';
            } else if (text.includes('service')) {
                buttonUrl.value = '/services';
            }
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/sliders/edit.blade.php ENDPATH**/ ?>