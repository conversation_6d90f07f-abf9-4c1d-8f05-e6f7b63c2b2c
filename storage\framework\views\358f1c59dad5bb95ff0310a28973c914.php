<?php $__env->startSection('title', 'Order Details'); ?>
<?php $__env->startSection('page-title', 'Order #' . $order->order_number); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.orders.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Orders
        </a>
        <a href="<?php echo e(route('admin.orders.edit', $order)); ?>" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Order
        </a>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i> Actions
            </button>
            <ul class="dropdown-menu">
                <?php if($order->canBeShipped()): ?>
                    <li>
                        <button type="button" class="dropdown-item" onclick="updateOrderStatus('shipped')">
                            <i class="fas fa-truck me-2"></i> Mark as Shipped
                        </button>
                    </li>
                <?php endif; ?>
                <?php if($order->isShipped()): ?>
                    <li>
                        <button type="button" class="dropdown-item" onclick="updateOrderStatus('delivered')">
                            <i class="fas fa-check-circle me-2"></i> Mark as Delivered
                        </button>
                    </li>
                <?php endif; ?>
                <?php if($order->canBeCancelled()): ?>
                    <li>
                        <button type="button" class="dropdown-item text-warning" onclick="updateOrderStatus('cancelled')">
                            <i class="fas fa-times me-2"></i> Cancel Order
                        </button>
                    </li>
                <?php endif; ?>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <button type="button" class="dropdown-item" onclick="printOrder()">
                        <i class="fas fa-print me-2"></i> Print Order
                    </button>
                </li>
                <li>
                    <button type="button" class="dropdown-item text-danger" onclick="deleteOrder()">
                        <i class="fas fa-trash me-2"></i> Delete Order
                    </button>
                </li>
            </ul>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Order Information -->
        <div class="col-lg-8">
            <!-- Order Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Order Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Order Number</label>
                                <p class="mb-0">
                                    <code><?php echo e($order->order_number); ?></code>
                                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('<?php echo e($order->order_number); ?>')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Customer</label>
                                <p class="mb-0">
                                    <strong><?php echo e($order->customer_name); ?></strong><br>
                                    <a href="mailto:<?php echo e($order->customer_email); ?>"><?php echo e($order->customer_email); ?></a><br>
                                    <?php if($order->customer_phone): ?>
                                        <a href="tel:<?php echo e($order->customer_phone); ?>"><?php echo e($order->customer_phone); ?></a>
                                    <?php endif; ?>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Order Date</label>
                                <p class="mb-0">
                                    <?php echo e($order->created_at->format('M d, Y h:i A')); ?><br>
                                    <small class="text-muted"><?php echo e($order->created_at->diffForHumans()); ?></small>
                                </p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status</label>
                                <div>
                                    <span class="badge bg-<?php echo e($order->status_badge_color); ?> fs-6" id="orderStatus">
                                        <?php echo e($order->formatted_status); ?>

                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Payment Status</label>
                                <div>
                                    <span class="badge bg-<?php echo e($order->payment_status_badge_color); ?> fs-6" id="paymentStatus">
                                        <?php echo e($order->formatted_payment_status); ?>

                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Payment Method</label>
                                <p class="mb-0">
                                    <?php if($order->payment_method): ?>
                                        <?php switch($order->payment_method):
                                            case ('manual'): ?>
                                                <i class="fas fa-money-bill-wave text-success me-2"></i>Manual Payment
                                                <?php break; ?>
                                            <?php case ('paypal'): ?>
                                                <i class="fab fa-paypal text-primary me-2"></i>PayPal
                                                <?php break; ?>
                                            <?php case ('stripe'): ?>
                                                <i class="fas fa-credit-card text-info me-2"></i>Credit/Debit Card (Stripe)
                                                <?php break; ?>
                                            <?php default: ?>
                                                <i class="fas fa-question-circle text-muted me-2"></i><?php echo e(ucfirst($order->payment_method)); ?>

                                        <?php endswitch; ?>
                                        <?php if($order->payment_reference): ?>
                                            <br><small class="text-muted">Reference: <?php echo e($order->payment_reference); ?></small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">Not specified</span>
                                    <?php endif; ?>
                                </p>
                            </div>

                            <?php if($order->tracking_number): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Tracking Number</label>
                                    <p class="mb-0">
                                        <code><?php echo e($order->tracking_number); ?></code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('<?php echo e($order->tracking_number); ?>')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        Order Items (<?php echo e($order->items->count()); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($item->product_image): ?>
                                                    <img src="<?php echo e(Storage::url($item->product_image)); ?>" 
                                                         alt="<?php echo e($item->product_name); ?>" 
                                                         class="me-3" 
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo e($item->product_name); ?></strong>
                                                    <?php if($item->product_description): ?>
                                                        <br><small class="text-muted"><?php echo e($item->product_description); ?></small>
                                                    <?php endif; ?>
                                                    <?php if($item->product): ?>
                                                        <br><a href="<?php echo e(route('admin.products.show', $item->product)); ?>" 
                                                               class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-external-link-alt"></i> View Product
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <code><?php echo e($item->product_sku); ?></code>
                                        </td>
                                        <td>
                                            <?php echo \App\Helpers\CurrencyHelper::format($item->unit_price); ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($item->quantity); ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo \App\Helpers\CurrencyHelper::format($item->total_price); ?></strong>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Addresses -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                Billing Address
                            </h6>
                        </div>
                        <div class="card-body">
                            <address class="mb-0">
                                <strong><?php echo e($order->billing_first_name); ?> <?php echo e($order->billing_last_name); ?></strong><br>
                                <?php if($order->billing_company): ?>
                                    <?php echo e($order->billing_company); ?><br>
                                <?php endif; ?>
                                <?php echo e($order->billing_address_1); ?><br>
                                <?php if($order->billing_address_2): ?>
                                    <?php echo e($order->billing_address_2); ?><br>
                                <?php endif; ?>
                                <?php echo e($order->billing_city); ?>, <?php echo e($order->billing_state); ?> <?php echo e($order->billing_postal_code); ?><br>
                                <?php echo e($order->billing_country); ?>

                            </address>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-truck me-2"></i>
                                Shipping Address
                            </h6>
                        </div>
                        <div class="card-body">
                            <address class="mb-0">
                                <strong><?php echo e($order->shipping_first_name); ?> <?php echo e($order->shipping_last_name); ?></strong><br>
                                <?php if($order->shipping_company): ?>
                                    <?php echo e($order->shipping_company); ?><br>
                                <?php endif; ?>
                                <?php echo e($order->shipping_address_1); ?><br>
                                <?php if($order->shipping_address_2): ?>
                                    <?php echo e($order->shipping_address_2); ?><br>
                                <?php endif; ?>
                                <?php echo e($order->shipping_city); ?>, <?php echo e($order->shipping_state); ?> <?php echo e($order->shipping_postal_code); ?><br>
                                <?php echo e($order->shipping_country); ?>

                            </address>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Order Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Order Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span><?php echo \App\Helpers\CurrencyHelper::format($order->subtotal); ?></span>
                    </div>
                    
                    <?php if($order->shipping_amount > 0): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping:</span>
                            <span><?php echo \App\Helpers\CurrencyHelper::format($order->shipping_amount); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($order->tax_amount > 0): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax:</span>
                            <span><?php echo \App\Helpers\CurrencyHelper::format($order->tax_amount); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($order->discount_amount > 0): ?>
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>Discount:</span>
                            <span>-<?php echo \App\Helpers\CurrencyHelper::format($order->discount_amount); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Total:</strong>
                        <strong class="h5"><?php echo \App\Helpers\CurrencyHelper::format($order->total_amount); ?></strong>
                    </div>
                </div>
            </div>

            <!-- Order Timeline -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Order Timeline
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Order Created</h6>
                                <p class="mb-0 text-muted"><?php echo e($order->created_at->format('M d, Y h:i A')); ?></p>
                            </div>
                        </div>
                        
                        <?php if($order->paid_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Payment Received</h6>
                                    <p class="mb-0 text-muted"><?php echo e($order->paid_at->format('M d, Y h:i A')); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if($order->shipped_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Order Shipped</h6>
                                    <p class="mb-0 text-muted"><?php echo e($order->shipped_at->format('M d, Y h:i A')); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if($order->delivered_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Order Delivered</h6>
                                    <p class="mb-0 text-muted"><?php echo e($order->delivered_at->format('M d, Y h:i A')); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if(!$order->isPaid()): ?>
                            <button type="button" class="btn btn-success" onclick="updatePaymentStatus('paid')">
                                <i class="fas fa-credit-card me-2"></i>
                                Mark as Paid
                            </button>
                        <?php endif; ?>
                        
                        <?php if($order->canBeShipped()): ?>
                            <button type="button" class="btn btn-primary" onclick="updateOrderStatus('shipped')">
                                <i class="fas fa-truck me-2"></i>
                                Mark as Shipped
                            </button>
                        <?php endif; ?>
                        
                        <?php if($order->isShipped()): ?>
                            <button type="button" class="btn btn-success" onclick="updateOrderStatus('delivered')">
                                <i class="fas fa-check-circle me-2"></i>
                                Mark as Delivered
                            </button>
                        <?php endif; ?>
                        
                        <button type="button" class="btn btn-outline-secondary" onclick="printOrder()">
                            <i class="fas fa-print me-2"></i>
                            Print Order
                        </button>
                        
                        <a href="<?php echo e(route('admin.orders.edit', $order)); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>
                            Edit Order
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content h6 {
        margin-bottom: 5px;
        font-size: 0.9rem;
    }
    
    .timeline-content p {
        font-size: 0.8rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function updateOrderStatus(status) {
        if (confirm(`Are you sure you want to update the order status to ${status}?`)) {
            fetch(`<?php echo e(route('admin.orders.update-status', $order)); ?>`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('orderStatus').textContent = data.status;
                    document.getElementById('orderStatus').className = `badge bg-${data.badge_color} fs-6`;
                    location.reload(); // Reload to update timeline and actions
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        }
    }

    function updatePaymentStatus(status) {
        if (confirm(`Are you sure you want to update the payment status to ${status}?`)) {
            fetch(`<?php echo e(route('admin.orders.update-payment-status', $order)); ?>`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({ payment_status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('paymentStatus').textContent = data.payment_status;
                    document.getElementById('paymentStatus').className = `badge bg-${data.badge_color} fs-6`;
                    location.reload(); // Reload to update timeline and actions
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        }
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        Copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        });
    }

    function printOrder() {
        window.print();
    }

    function deleteOrder() {
        if (confirm('Are you sure you want to delete this order? This action cannot be undone.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?php echo e(route("admin.orders.destroy", $order)); ?>';
            form.innerHTML = `
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
            `;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/orders/show.blade.php ENDPATH**/ ?>