@extends('layouts.frontend')

@section('title', $career->title . ' - Careers at ' . ($siteSettings['site_name'] ?? 'Atrix Logistics'))
@section('meta_description', Str::limit(strip_tags($career->description), 160))

@section('content')
    <!-- Career Header with Breadcrumbs -->
    <x-page-hero
        title="{{ $career->title }}"
        subtitle="{{ $career->department ? $career->department . ' • ' : '' }}{{ $career->location }} • {{ ucfirst(str_replace('-', ' ', $career->employment_type)) }} • {{ ucfirst($career->experience_level) }} Level"
        :featured="$career->is_featured"
        :breadcrumbs="[
            ['title' => 'Home', 'url' => route('home')],
            ['title' => 'Careers', 'url' => route('careers.index')],
            ['title' => $career->title]
        ]"
        gradient="from-blue-600 to-green-600"
    >
        <div class="flex flex-col sm:flex-row gap-4 justify-center mt-8">
            @if(!$career->isApplicationDeadlinePassed())
                <a href="{{ route('careers.apply', $career->slug) }}"
                   class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium transition-colors text-center">
                    Apply for This Position
                </a>
            @else
                <span class="bg-gray-500 text-white px-8 py-3 rounded-lg font-medium text-center cursor-not-allowed">
                    Applications Closed
                </span>
            @endif
            <a href="{{ route('careers.index') }}"
               class="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-lg font-medium transition-colors text-center border border-white/30">
                View All Positions
            </a>
        </div>
    </x-page-hero>

    <!-- Career Details -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="grid lg:grid-cols-3 gap-8">
                    <!-- Main Content -->
                    <div class="lg:col-span-2 space-y-8">
                        <!-- Job Description -->
                        <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Job Description</h2>
                            <div class="prose prose-lg max-w-none text-gray-700">
                                {!! nl2br(e($career->description)) !!}
                            </div>
                        </div>

                        <!-- Responsibilities -->
                        @if($career->responsibilities)
                            <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">Key Responsibilities</h2>
                                <div class="prose prose-lg max-w-none text-gray-700">
                                    {!! nl2br(e($career->responsibilities)) !!}
                                </div>
                            </div>
                        @endif

                        <!-- Requirements -->
                        @if($career->requirements)
                            <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">Requirements</h2>
                                <div class="prose prose-lg max-w-none text-gray-700">
                                    {!! nl2br(e($career->requirements)) !!}
                                </div>
                            </div>
                        @endif

                        <!-- Benefits -->
                        @if($career->benefits)
                            <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">Benefits & Perks</h2>
                                <div class="prose prose-lg max-w-none text-gray-700">
                                    {!! nl2br(e($career->benefits)) !!}
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Quick Info -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Position Details</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Salary:</span>
                                    <span class="font-medium text-green-600">{{ $career->formatted_salary }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Type:</span>
                                    <span class="font-medium">{{ ucfirst(str_replace('-', ' ', $career->employment_type)) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Level:</span>
                                    <span class="font-medium">{{ ucfirst($career->experience_level) }}</span>
                                </div>
                                @if($career->application_deadline)
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Deadline:</span>
                                        <span class="font-medium text-red-600">{{ $career->application_deadline->format('M d, Y') }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Required Skills -->
                        @if($career->required_skills && count($career->required_skills) > 0)
                            <div class="bg-white rounded-lg shadow-lg p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Required Skills</h3>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($career->required_skills as $skill)
                                        <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                                            {{ $skill }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Preferred Skills -->
                        @if($career->preferred_skills && count($career->preferred_skills) > 0)
                            <div class="bg-white rounded-lg shadow-lg p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Preferred Skills</h3>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($career->preferred_skills as $skill)
                                        <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                                            {{ $skill }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Contact Info -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Contact Information</h3>
                            <div class="space-y-3">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                    <a href="mailto:{{ $career->contact_email }}" class="text-blue-600 hover:text-blue-800">
                                        {{ $career->contact_email }}
                                    </a>
                                </div>
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-phone text-gray-400"></i>
                                    <a href="tel:{{ $career->contact_phone }}" class="text-blue-600 hover:text-blue-800">
                                        {{ $career->contact_phone }}
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Apply Button (Sticky) -->
                        @if(!$career->isApplicationDeadlinePassed())
                            <div class="sticky top-4">
                                <a href="{{ route('careers.apply', $career->slug) }}" 
                                   class="block w-full bg-green-600 hover:bg-green-700 text-white px-6 py-4 rounded-lg font-medium transition-colors text-center">
                                    Apply for This Position
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Positions -->
    @php
        $relatedCareers = \App\Models\Career::active()
            ->where('id', '!=', $career->id)
            ->where(function($query) use ($career) {
                $query->where('department', $career->department)
                      ->orWhere('experience_level', $career->experience_level);
            })
            ->limit(3)
            ->get();
    @endphp

    @if($relatedCareers->count() > 0)
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Related Positions</h2>
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($relatedCareers as $relatedCareer)
                            <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $relatedCareer->title }}</h3>
                                <div class="text-sm text-gray-600 mb-3">
                                    {{ $relatedCareer->department }} • {{ $relatedCareer->location }}
                                </div>
                                <p class="text-gray-700 mb-4 line-clamp-3">
                                    {{ Str::limit(strip_tags($relatedCareer->description), 100) }}
                                </p>
                                <a href="{{ route('careers.show', $relatedCareer->slug) }}" 
                                   class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-medium transition-colors">
                                    View Details
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </section>
    @endif
@endsection
