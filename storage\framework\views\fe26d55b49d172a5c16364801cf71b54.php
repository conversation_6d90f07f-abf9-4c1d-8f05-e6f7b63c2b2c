<?php $__env->startSection('title', 'Payment Successful'); ?>
<?php $__env->startSection('page-title', 'Payment Confirmation'); ?>

<?php $__env->startSection('content'); ?>
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Success Message -->
        <div class="text-center mb-4">
            <?php if($payment->status === 'completed'): ?>
                <div class="success-icon mb-3">
                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                </div>
                <h2 class="text-success mb-2">Payment Successful!</h2>
                <p class="lead text-muted">Your payment has been processed successfully.</p>
            <?php elseif($payment->status === 'pending_approval'): ?>
                <div class="success-icon mb-3">
                    <i class="fas fa-clock text-warning" style="font-size: 4rem;"></i>
                </div>
                <h2 class="text-warning mb-2">Payment Request Submitted!</h2>
                <p class="lead text-muted">Your payment request has been submitted for admin approval.</p>
            <?php else: ?>
                <div class="success-icon mb-3">
                    <i class="fas fa-hourglass-half text-info" style="font-size: 4rem;"></i>
                </div>
                <h2 class="text-info mb-2">Payment Processing!</h2>
                <p class="lead text-muted">Your payment is being processed.</p>
            <?php endif; ?>
        </div>

        <!-- Payment Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    Payment Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Payment ID</label>
                        <p class="mb-0">
                            <code>#<?php echo e(str_pad($payment->id, 6, '0', STR_PAD_LEFT)); ?></code>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Transaction ID</label>
                        <p class="mb-0">
                            <?php if($payment->transaction_id): ?>
                                <code><?php echo e($payment->transaction_id); ?></code>
                            <?php else: ?>
                                <span class="text-muted">Not available</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Amount Paid</label>
                        <p class="mb-0 h5 text-success"><?php echo e($payment->formatted_amount); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Payment Method</label>
                        <p class="mb-0"><?php echo e($payment->payment_method_name); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Status</label>
                        <p class="mb-0">
                            <span class="badge bg-<?php echo e($payment->status_badge_color); ?> fs-6">
                                <?php echo e($payment->formatted_status); ?>

                            </span>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Date & Time</label>
                        <p class="mb-0"><?php echo e($payment->created_at->format('M d, Y \a\t g:i A')); ?></p>
                    </div>
                </div>

                <?php if($payment->status === 'pending_approval'): ?>
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>What happens next?</h6>
                    <ul class="mb-0">
                        <li>Our team will review your payment request within 24 hours</li>
                        <li>You'll receive payment instructions via email</li>
                        <li>Once payment is received and verified, your parcel status will be updated</li>
                        <li>You'll receive a confirmation email when payment is processed</li>
                    </ul>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Parcel Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    Parcel Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Tracking Number</label>
                        <p class="mb-0">
                            <code class="fs-5"><?php echo e($payment->parcel->tracking_number); ?></code>
                            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('<?php echo e($payment->parcel->tracking_number); ?>')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Service Type</label>
                        <p class="mb-0"><?php echo e(ucfirst(str_replace('_', ' ', $payment->parcel->service_type))); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Recipient</label>
                        <p class="mb-0"><?php echo e($payment->parcel->recipient_name); ?></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold text-muted">Current Status</label>
                        <p class="mb-0">
                            <span class="badge bg-<?php echo e($payment->parcel->status === 'delivered' ? 'success' : ($payment->parcel->status === 'in_transit' ? 'primary' : 'warning')); ?>">
                                <?php echo e(ucfirst(str_replace('_', ' ', $payment->parcel->status))); ?>

                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list-check me-2"></i>
                    What's Next?
                </h5>
            </div>
            <div class="card-body">
                <?php if($payment->status === 'completed'): ?>
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-start mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-success fa-lg mt-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Payment Confirmed</h6>
                                <p class="mb-0 text-muted">Your payment has been successfully processed and confirmed.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-start mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-envelope text-primary fa-lg mt-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Email Confirmation</h6>
                                <p class="mb-0 text-muted">A confirmation email has been sent to your registered email address.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-start mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-truck text-warning fa-lg mt-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Shipment Processing</h6>
                                <p class="mb-0 text-muted">Your parcel will be processed and shipped according to the selected service.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-start mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-search-location text-info fa-lg mt-1"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Track Your Package</h6>
                                <p class="mb-0 text-muted">You can track your package anytime using the tracking number.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-clock me-2"></i>Pending Payment</h6>
                    <p class="mb-0">Your parcel will be processed once payment is confirmed. You'll receive updates via email and SMS.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="d-grid">
                    <a href="<?php echo e(route('customer.parcels.show', $payment->parcel)); ?>" class="btn btn-primary">
                        <i class="fas fa-box me-2"></i>
                        View Parcel Details
                    </a>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="d-grid">
                    <a href="<?php echo e(route('customer.track', ['tracking_number' => $payment->parcel->tracking_number])); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-search-location me-2"></i>
                        Track Package
                    </a>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="d-grid">
                    <button class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        Print Receipt
                    </button>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="d-grid">
                    <a href="<?php echo e(route('customer.dashboard')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Support Information -->
        <div class="card mt-4">
            <div class="card-body text-center">
                <h6 class="mb-2">Need Help?</h6>
                <p class="text-muted mb-3">If you have any questions about your payment or shipment, our support team is here to help.</p>
                <a href="<?php echo e(route('customer.support.create')); ?>" class="btn btn-outline-warning">
                    <i class="fas fa-headset me-2"></i>
                    Contact Support
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.success-icon {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

@media print {
    .btn, .card-header {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    Tracking number copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert('Tracking number: ' + text);
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/customer/payments/success.blade.php ENDPATH**/ ?>