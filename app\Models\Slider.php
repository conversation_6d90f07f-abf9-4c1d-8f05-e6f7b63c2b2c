<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class Slider extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'subtitle',
        'description',
        'image',
        'mobile_image',
        'button_text',
        'button_url',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    protected $attributes = [
        'is_active' => false, // Default to inactive
        'sort_order' => 0,
    ];

    /**
     * Get the image URL
     */
    public function getImageUrlAttribute(): ?string
    {
        return $this->image ? Storage::url($this->image) : null;
    }

    /**
     * Get the mobile image URL
     */
    public function getMobileImageUrlAttribute(): ?string
    {
        return $this->mobile_image ? Storage::url($this->mobile_image) : null;
    }

    /**
     * Get active sliders
     */
    public static function active()
    {
        return static::where('is_active', true)
                    ->orderBy('sort_order')
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Get sliders for frontend display
     */
    public static function forDisplay()
    {
        return static::active()->get();
    }

    /**
     * Check if slider has a call-to-action button
     */
    public function hasButton(): bool
    {
        return !empty($this->button_text) && !empty($this->button_url);
    }

    /**
     * Get short description
     */
    public function getShortDescriptionAttribute(): string
    {
        return $this->description ? \Str::limit($this->description, 200) : '';
    }

    /**
     * Scope for ordering by display order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    /**
     * Scope for active sliders
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the appropriate image for device type
     */
    public function getImageForDevice(string $device = 'desktop'): ?string
    {
        if ($device === 'mobile' && $this->mobile_image) {
            return $this->getMobileImageUrlAttribute();
        }

        return $this->getImageUrlAttribute();
    }

    /**
     * Get slider data for frontend
     */
    public function toFrontendArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'description' => $this->description,
            'image' => $this->getImageUrlAttribute(),
            'mobile_image' => $this->getMobileImageUrlAttribute(),
            'button_text' => $this->button_text,
            'button_url' => $this->button_url,
            'has_button' => $this->hasButton(),
        ];
    }
}
