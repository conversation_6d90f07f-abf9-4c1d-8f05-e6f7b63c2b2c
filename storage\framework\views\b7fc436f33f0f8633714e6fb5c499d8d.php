<?php $__env->startSection('title', 'Products'); ?>
<?php $__env->startSection('page-title', 'Product Management'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.products.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add Product
        </a>
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-cog me-1"></i> Actions
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="toggleBulkMode()">
                <i class="fas fa-check-square me-2"></i> Bulk Actions
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="alert('Coming soon!')">
                <i class="fas fa-download me-2"></i> Export Products
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="alert('Coming soon!')">
                <i class="fas fa-upload me-2"></i> Import Products
            </a></li>
        </ul>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($products->total()); ?></h4>
                            <p class="mb-0">Total Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-box fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($products->where('is_active', true)->count()); ?></h4>
                            <p class="mb-0">Active Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($products->where('is_featured', true)->count()); ?></h4>
                            <p class="mb-0">Featured Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo e($categories->count()); ?></h4>
                            <p class="mb-0">Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-folder fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.products.index')); ?>" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo e(request('search')); ?>" placeholder="Product name, SKU...">
                </div>
                <div class="col-md-2">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                            <?php $__currentLoopData = $category->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($child->id); ?>" <?php echo e(request('category') == $child->id ? 'selected' : ''); ?>>
                                    └─ <?php echo e($child->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="stock_status" class="form-label">Stock Status</label>
                    <select class="form-select" id="stock_status" name="stock_status">
                        <option value="">All Stock</option>
                        <?php $__currentLoopData = $stockStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" <?php echo e(request('stock_status') == $key ? 'selected' : ''); ?>>
                                <?php echo e($label); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> Filter
                        </button>
                        <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    Products
                </h5>
                <div id="bulk-actions" class="d-none">
                    <form method="POST" action="<?php echo e(route('admin.products.bulk-action')); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <div class="input-group">
                            <select name="action" class="form-select" required>
                                <option value="">Bulk Actions</option>
                                <option value="activate">Activate</option>
                                <option value="deactivate">Deactivate</option>
                                <option value="delete">Delete</option>
                            </select>
                            <button type="submit" class="btn btn-primary">Apply</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="toggleBulkMode()">Cancel</button>
                        </div>
                        <input type="hidden" name="products" id="bulk-products">
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if($products->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%" class="bulk-checkbox d-none">
                                    <input type="checkbox" id="select-all" class="form-check-input">
                                </th>
                                <th width="40%">Product</th>
                                <th width="10%">Category</th>
                                <th width="10%">Price</th>
                                <th width="10%">Stock</th>
                                <th width="10%">Status</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="<?php echo e($product->id); ?>">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($product->featured_image): ?>
                                                <img src="<?php echo e(Storage::url($product->featured_image)); ?>" 
                                                     alt="<?php echo e($product->name); ?>" 
                                                     class="rounded me-3" 
                                                     style="width: 48px; height: 48px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div>
                                                <strong><?php echo e($product->name); ?></strong>
                                                <br><small class="text-muted">SKU: <?php echo e($product->sku); ?></small>
                                                <?php if($product->short_description): ?>
                                                    <br><small class="text-muted"><?php echo e(Str::limit($product->short_description, 60)); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($product->category): ?>
                                            <a href="<?php echo e(route('admin.categories.show', $product->category)); ?>" 
                                               class="text-decoration-none">
                                                <?php echo e($product->category->name); ?>

                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">No Category</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($product->isOnSale()): ?>
                                            <span class="text-decoration-line-through text-muted small"><?php echo \App\Helpers\CurrencyHelper::format($product->price); ?></span>
                                            <br><strong class="text-success"><?php echo \App\Helpers\CurrencyHelper::format($product->sale_price); ?></strong>
                                            <br><small class="badge bg-danger"><?php echo e($product->getDiscountPercentage()); ?>% OFF</small>
                                        <?php else: ?>
                                            <strong><?php echo \App\Helpers\CurrencyHelper::format($product->price); ?></strong>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($product->manage_stock): ?>
                                            <?php if($product->isInStock()): ?>
                                                <span class="badge bg-success"><?php echo e($product->stock_quantity); ?></span>
                                            <?php elseif($product->isLowStock()): ?>
                                                <span class="badge bg-warning"><?php echo e($product->stock_quantity); ?> (Low)</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Out of Stock</span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-info"><?php echo e(ucwords(str_replace('_', ' ', $product->stock_status))); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <form method="POST" action="<?php echo e(route('admin.products.toggle-status', $product)); ?>" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                <?php if($product->is_active): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </button>
                                        </form>
                                        <?php if($product->is_featured): ?>
                                            <span class="badge bg-warning ms-1">Featured</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.products.show', $product)); ?>" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.products.edit', $product)); ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(<?php echo e($product->id); ?>, '<?php echo e($product->name); ?>')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    <?php echo e($products->appends(request()->query())->links('pagination.admin')); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No products found</h5>
                    <?php if(request()->hasAny(['search', 'category', 'status', 'stock_status'])): ?>
                        <p class="text-muted">Try adjusting your filters or search terms.</p>
                        <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>
                            Clear Filters
                        </a>
                    <?php else: ?>
                        <p class="text-muted">Start by creating your first product.</p>
                        <a href="<?php echo e(route('admin.products.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create First Product
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Delete Product
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the product <strong id="deleteProductName"></strong>?</p>
                    <p class="text-muted">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" id="deleteForm" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            Delete Product
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Delete product
    function deleteProduct(productId, productName) {
        document.getElementById('deleteProductName').textContent = productName;
        document.getElementById('deleteForm').action = `/admin/ecommerce/products/${productId}`;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    // Bulk actions
    function toggleBulkMode() {
        const bulkCheckboxes = document.querySelectorAll('.bulk-checkbox');
        const bulkActions = document.getElementById('bulk-actions');
        
        bulkCheckboxes.forEach(checkbox => {
            checkbox.classList.toggle('d-none');
        });
        
        bulkActions.classList.toggle('d-none');
        
        // Clear all checkboxes
        document.querySelectorAll('.product-checkbox').forEach(cb => cb.checked = false);
        document.getElementById('select-all').checked = false;
    }

    // Select all functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkProducts();
    });

    // Update bulk products input
    function updateBulkProducts() {
        const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
        const productIds = Array.from(checkedBoxes).map(cb => cb.value);
        document.getElementById('bulk-products').value = JSON.stringify(productIds);
    }

    // Add event listeners to product checkboxes
    document.querySelectorAll('.product-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkProducts);
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/products/index.blade.php ENDPATH**/ ?>