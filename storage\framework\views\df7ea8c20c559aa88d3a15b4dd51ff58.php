<?php $__env->startSection('title', 'Order Confirmation'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success Header -->
        <div class="text-center mb-8">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900">Order Confirmed!</h1>
            <p class="mt-2 text-lg text-gray-600">Thank you for your order. We'll send you a confirmation email shortly.</p>
        </div>

        <!-- Order Details -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <!-- Order Header -->
            <div class="bg-green-50 px-6 py-4 border-b border-green-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">Order #<?php echo e($order->order_number); ?></h2>
                        <p class="text-sm text-gray-600">Placed on <?php echo e($order->created_at->format('F j, Y \a\t g:i A')); ?></p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            <?php echo e($order->formatted_status); ?>

                        </span>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="px-6 py-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                <div class="space-y-4">
                    <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center space-x-4 py-4 border-b border-gray-200 last:border-b-0">
                        <div class="flex-shrink-0">
                            <?php if($item->product && $item->product->featured_image_url): ?>
                                <img src="<?php echo e($item->product->featured_image_url); ?>" 
                                     alt="<?php echo e($item->product_name); ?>" 
                                     class="h-16 w-16 object-cover rounded-lg">
                            <?php else: ?>
                                <div class="h-16 w-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-box text-gray-400"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900"><?php echo e($item->product_name); ?></h4>
                            <?php if($item->product_sku): ?>
                                <p class="text-sm text-gray-500">SKU: <?php echo e($item->product_sku); ?></p>
                            <?php endif; ?>
                            <p class="text-sm text-gray-600">Quantity: <?php echo e($item->quantity); ?></p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-gray-900">$<?php echo e(number_format($item->total_price, 2)); ?></p>
                            <p class="text-sm text-gray-500">$<?php echo e(number_format($item->unit_price, 2)); ?> each</p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="bg-gray-50 px-6 py-4">
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Subtotal</span>
                        <span class="text-gray-900">$<?php echo e(number_format($order->subtotal, 2)); ?></span>
                    </div>
                    <?php if($order->tax_amount > 0): ?>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Tax</span>
                        <span class="text-gray-900">$<?php echo e(number_format($order->tax_amount, 2)); ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if($order->shipping_amount > 0): ?>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Shipping</span>
                        <span class="text-gray-900">$<?php echo e(number_format($order->shipping_amount, 2)); ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if($order->discount_amount > 0): ?>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Discount</span>
                        <span class="text-red-600">-$<?php echo e(number_format($order->discount_amount, 2)); ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="border-t border-gray-200 pt-2">
                        <div class="flex justify-between">
                            <span class="text-lg font-semibold text-gray-900">Total</span>
                            <span class="text-lg font-semibold text-green-600">$<?php echo e(number_format($order->total_amount, 2)); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shipping & Billing Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            <!-- Shipping Address -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
                <div class="text-sm text-gray-600 space-y-1">
                    <p class="font-medium text-gray-900"><?php echo e($order->shipping_first_name); ?> <?php echo e($order->shipping_last_name); ?></p>
                    <?php if($order->shipping_company): ?>
                        <p><?php echo e($order->shipping_company); ?></p>
                    <?php endif; ?>
                    <p><?php echo e($order->shipping_address_1); ?></p>
                    <?php if($order->shipping_address_2): ?>
                        <p><?php echo e($order->shipping_address_2); ?></p>
                    <?php endif; ?>
                    <p><?php echo e($order->shipping_city); ?>, <?php echo e($order->shipping_state); ?> <?php echo e($order->shipping_postal_code); ?></p>
                    <p><?php echo e($order->shipping_country); ?></p>
                </div>
            </div>

            <!-- Billing Address -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Billing Address</h3>
                <div class="text-sm text-gray-600 space-y-1">
                    <p class="font-medium text-gray-900"><?php echo e($order->billing_first_name); ?> <?php echo e($order->billing_last_name); ?></p>
                    <?php if($order->billing_company): ?>
                        <p><?php echo e($order->billing_company); ?></p>
                    <?php endif; ?>
                    <p><?php echo e($order->billing_address_1); ?></p>
                    <?php if($order->billing_address_2): ?>
                        <p><?php echo e($order->billing_address_2); ?></p>
                    <?php endif; ?>
                    <p><?php echo e($order->billing_city); ?>, <?php echo e($order->billing_state); ?> <?php echo e($order->billing_postal_code); ?></p>
                    <p><?php echo e($order->billing_country); ?></p>
                </div>
            </div>
        </div>

        <!-- Payment Info -->
        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Information</h3>
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600">Payment Method</p>
                    <p class="font-medium text-gray-900"><?php echo e($order->formatted_payment_method); ?></p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Payment Status</p>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                        <?php if($order->payment_status === 'paid'): ?> bg-green-100 text-green-800
                        <?php elseif($order->payment_status === 'pending'): ?> bg-yellow-100 text-yellow-800
                        <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                        <?php echo e($order->formatted_payment_status); ?>

                    </span>
                </div>
            </div>
            
            <?php if($order->payment_method === 'manual' && $order->payment_status === 'pending'): ?>
            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex">
                    <i class="fas fa-info-circle text-yellow-400 mt-0.5 mr-3"></i>
                    <div>
                        <h4 class="text-sm font-medium text-yellow-800">Payment Instructions</h4>
                        <p class="text-sm text-yellow-700 mt-1">
                            Payment instructions will be sent via email. Please check your inbox for details on how to complete your payment.
                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Order Notes -->
        <?php if($order->notes): ?>
        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Notes</h3>
            <p class="text-gray-600"><?php echo e($order->notes); ?></p>
        </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 mt-8">
            <a href="<?php echo e(route('customer.orders')); ?>" 
               class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold text-center transition-colors">
                View All Orders
            </a>
            <a href="<?php echo e(route('home')); ?>" 
               class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg font-semibold text-center transition-colors">
                Continue Shopping
            </a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/frontend/checkout/confirmation.blade.php ENDPATH**/ ?>