@extends('layouts.admin')

@section('title', 'Slider Preview')
@section('page-title', 'Slider Preview')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.cms.sliders.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Sliders
        </a>
        <a href="{{ route('admin.cms.sliders.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add New Slider
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            @if($sliders->count() > 0)
                <!-- Preview Controls -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Preview Controls</h6>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="deviceType" class="form-label">Device Type</label>
                                    <select id="deviceType" class="form-select" onchange="switchDevice()">
                                        <option value="desktop">Desktop View</option>
                                        <option value="mobile">Mobile View</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Auto-play</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoPlay" checked onchange="toggleAutoPlay()">
                                        <label class="form-check-label" for="autoPlay">
                                            Enable auto-play
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slider Preview -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            Live Preview - {{ $sliders->count() }} Active Slider(s)
                        </h6>
                        <div class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            This is how your sliders will appear on the frontend
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Desktop Preview -->
                        <div id="desktop-preview" class="preview-container">
                            <div id="sliderCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
                                <div class="carousel-indicators">
                                    @foreach($sliders as $index => $slider)
                                        <button type="button" data-bs-target="#sliderCarousel" data-bs-slide-to="{{ $index }}" 
                                                class="{{ $index === 0 ? 'active' : '' }}" 
                                                aria-current="{{ $index === 0 ? 'true' : 'false' }}" 
                                                aria-label="Slide {{ $index + 1 }}"></button>
                                    @endforeach
                                </div>
                                
                                <div class="carousel-inner">
                                    @foreach($sliders as $index => $slider)
                                        <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                            <div class="position-relative">
                                                @if($slider->image)
                                                    <img src="{{ Storage::url($slider->image) }}" 
                                                         class="d-block w-100 slider-image" 
                                                         alt="{{ $slider->title }}"
                                                         style="height: 500px; object-fit: cover;">
                                                @else
                                                    <div class="bg-secondary d-flex align-items-center justify-content-center" 
                                                         style="height: 500px;">
                                                        <i class="fas fa-image fa-4x text-white-50"></i>
                                                    </div>
                                                @endif
                                                
                                                <!-- Overlay Content -->
                                                <div class="carousel-caption d-block">
                                                    <div class="container">
                                                        <div class="row">
                                                            <div class="col-lg-8">
                                                                @if($slider->subtitle)
                                                                    <p class="lead text-white-75 mb-2">{{ $slider->subtitle }}</p>
                                                                @endif
                                                                <h1 class="display-4 fw-bold text-white mb-3">{{ $slider->title }}</h1>
                                                                @if($slider->description)
                                                                    <p class="lead text-white-75 mb-4">{{ $slider->description }}</p>
                                                                @endif
                                                                @if($slider->hasButton())
                                                                    <a href="{{ $slider->button_url }}" class="btn btn-primary btn-lg">
                                                                        {{ $slider->button_text }}
                                                                    </a>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                
                                <button class="carousel-control-prev" type="button" data-bs-target="#sliderCarousel" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Previous</span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#sliderCarousel" data-bs-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Next</span>
                                </button>
                            </div>
                        </div>

                        <!-- Mobile Preview -->
                        <div id="mobile-preview" class="preview-container" style="display: none;">
                            <div class="mobile-frame mx-auto" style="max-width: 375px;">
                                <div id="mobileSliderCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
                                    <div class="carousel-indicators">
                                        @foreach($sliders as $index => $slider)
                                            <button type="button" data-bs-target="#mobileSliderCarousel" data-bs-slide-to="{{ $index }}" 
                                                    class="{{ $index === 0 ? 'active' : '' }}" 
                                                    aria-current="{{ $index === 0 ? 'true' : 'false' }}" 
                                                    aria-label="Mobile Slide {{ $index + 1 }}"></button>
                                        @endforeach
                                    </div>
                                    
                                    <div class="carousel-inner">
                                        @foreach($sliders as $index => $slider)
                                            <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                                <div class="position-relative">
                                                    @php
                                                        $mobileImage = $slider->mobile_image ? Storage::url($slider->mobile_image) : ($slider->image ? Storage::url($slider->image) : null);
                                                    @endphp
                                                    
                                                    @if($mobileImage)
                                                        <img src="{{ $mobileImage }}" 
                                                             class="d-block w-100" 
                                                             alt="{{ $slider->title }}"
                                                             style="height: 400px; object-fit: cover;">
                                                    @else
                                                        <div class="bg-secondary d-flex align-items-center justify-content-center" 
                                                             style="height: 400px;">
                                                            <i class="fas fa-image fa-3x text-white-50"></i>
                                                        </div>
                                                    @endif
                                                    
                                                    <!-- Mobile Overlay Content -->
                                                    <div class="carousel-caption d-block">
                                                        <div class="text-center">
                                                            @if($slider->subtitle)
                                                                <p class="text-white-75 mb-2 small">{{ $slider->subtitle }}</p>
                                                            @endif
                                                            <h3 class="fw-bold text-white mb-2">{{ $slider->title }}</h3>
                                                            @if($slider->description)
                                                                <p class="text-white-75 mb-3 small">{{ Str::limit($slider->description, 100) }}</p>
                                                            @endif
                                                            @if($slider->hasButton())
                                                                <a href="{{ $slider->button_url }}" class="btn btn-primary btn-sm">
                                                                    {{ $slider->button_text }}
                                                                </a>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    
                                    <button class="carousel-control-prev" type="button" data-bs-target="#mobileSliderCarousel" data-bs-slide="prev">
                                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                        <span class="visually-hidden">Previous</span>
                                    </button>
                                    <button class="carousel-control-next" type="button" data-bs-target="#mobileSliderCarousel" data-bs-slide="next">
                                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                        <span class="visually-hidden">Next</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slider Details -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Active Sliders Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Order</th>
                                        <th>Title</th>
                                        <th>Images</th>
                                        <th>Button</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sliders as $slider)
                                        <tr>
                                            <td>{{ $slider->sort_order }}</td>
                                            <td>
                                                <strong>{{ $slider->title }}</strong>
                                                @if($slider->subtitle)
                                                    <br><small class="text-muted">{{ $slider->subtitle }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    @if($slider->image)
                                                        <span class="badge bg-success">Desktop</span>
                                                    @endif
                                                    @if($slider->mobile_image)
                                                        <span class="badge bg-info">Mobile</span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                @if($slider->hasButton())
                                                    <span class="badge bg-primary">{{ $slider->button_text }}</span>
                                                @else
                                                    <span class="text-muted">No button</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('admin.cms.sliders.edit', $slider) }}" class="btn btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{{ route('admin.cms.sliders.show', $slider) }}" class="btn btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @else
                <!-- No Sliders -->
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-images fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted mb-3">No Active Sliders</h4>
                        <p class="text-muted mb-4">There are no active sliders to preview. Create and activate some sliders to see them here.</p>
                        <a href="{{ route('admin.cms.sliders.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Create Your First Slider
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('styles')
<style>
    .preview-container {
        background: #f8f9fa;
    }
    
    .mobile-frame {
        border: 8px solid #333;
        border-radius: 25px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0,0,0,0.3);
    }
    
    .carousel-caption {
        background: linear-gradient(transparent, rgba(0,0,0,0.7));
        bottom: 0;
        left: 0;
        right: 0;
        padding: 3rem 1rem 2rem;
    }
    
    .text-white-75 {
        color: rgba(255,255,255,0.75) !important;
    }
</style>
@endpush

@push('scripts')
<script>
    function switchDevice() {
        const deviceType = document.getElementById('deviceType').value;
        const desktopPreview = document.getElementById('desktop-preview');
        const mobilePreview = document.getElementById('mobile-preview');
        
        if (deviceType === 'mobile') {
            desktopPreview.style.display = 'none';
            mobilePreview.style.display = 'block';
        } else {
            desktopPreview.style.display = 'block';
            mobilePreview.style.display = 'none';
        }
    }
    
    function toggleAutoPlay() {
        const autoPlay = document.getElementById('autoPlay').checked;
        const carousels = document.querySelectorAll('.carousel');
        
        carousels.forEach(carousel => {
            const bsCarousel = bootstrap.Carousel.getInstance(carousel);
            if (bsCarousel) {
                if (autoPlay) {
                    bsCarousel.cycle();
                } else {
                    bsCarousel.pause();
                }
            }
        });
    }
</script>
@endpush
