<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Services\ImageProcessingService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Session;

class ProductController extends Controller
{
    protected ImageProcessingService $imageService;

    public function __construct(ImageProcessingService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Product::with(['category']);

        // Apply filters
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($request->filled('stock_status')) {
            $query->byStockStatus($request->stock_status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $products = $query->latest()->paginate(20);

        // Get filter options
        $categories = Category::active()->orderBy('name')->get();
        $stockStatuses = [
            'in_stock' => 'In Stock',
            'low_stock' => 'Low Stock',
            'out_of_stock' => 'Out of Stock',
            'on_backorder' => 'On Backorder',
        ];

        return view('admin.products.index', compact('products', 'categories', 'stockStatuses'));
    }

    /**
     * Show the form for creating a new resource (Step 1)
     */
    public function create(): View
    {
        // Clear any existing session data
        Session::forget('product_form_data');

        $categories = Category::active()
                            ->with('children')
                            ->orderBy('sort_order')
                            ->get();

        return view('admin.products.create.step1', compact('categories'));
    }

    /**
     * Handle Step 1 form submission
     */
    public function storeStep1(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:products,slug',
            'sku' => 'nullable|string|max:100|unique:products,sku',
            'category_id' => 'required|exists:categories,id',
            'short_description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_digital' => 'boolean',
            'is_virtual' => 'boolean',
        ]);

        $validated['is_active'] = $request->boolean('is_active');
        $validated['is_featured'] = $request->boolean('is_featured');
        $validated['is_digital'] = $request->boolean('is_digital');
        $validated['is_virtual'] = $request->boolean('is_virtual');

        // Store in session
        Session::put('product_form_data.step1', $validated);

        return redirect()->route('admin.products.create.step2');
    }

    /**
     * Show Step 2 form (Content & SEO)
     */
    public function createStep2()
    {
        $step1Data = Session::get('product_form_data.step1');

        if (!$step1Data) {
            return redirect()->route('admin.products.create')
                           ->with('error', 'Please complete Step 1 first.');
        }

        return view('admin.products.create.step2', compact('step1Data'));
    }

    /**
     * Handle Step 2 form submission
     */
    public function storeStep2(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'description' => 'nullable|string',
            'additional_information' => 'nullable|string',
            'technical_specifications' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ]);

        // Convert tags string to array
        if ($validated['tags']) {
            $validated['tags'] = array_map('trim', explode(',', $validated['tags']));
        }

        // Store in session
        Session::put('product_form_data.step2', $validated);

        return redirect()->route('admin.products.create.step3');
    }

    /**
     * Show Step 3 form (Pricing & Inventory)
     */
    public function createStep3()
    {
        $step1Data = Session::get('product_form_data.step1');
        $step2Data = Session::get('product_form_data.step2');

        if (!$step1Data || !$step2Data) {
            return redirect()->route('admin.products.create')
                           ->with('error', 'Please complete previous steps first.');
        }

        return view('admin.products.create.step3', compact('step1Data', 'step2Data'));
    }

    /**
     * Handle Step 3 form submission
     */
    public function storeStep3(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'hide_price' => 'boolean',
            'manage_stock' => 'boolean',
            'stock_quantity' => 'nullable|integer|min:0',
            'min_stock_level' => 'nullable|integer|min:0',
            'stock_status' => 'required|in:in_stock,out_of_stock,on_backorder',
            'weight' => 'nullable|numeric|min:0',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',
            'shipping_class' => 'nullable|string|max:100',
            'tax_class' => 'nullable|string|max:100',
            'date_on_sale_from' => 'nullable|date',
            'date_on_sale_to' => 'nullable|date|after:date_on_sale_from',
        ]);

        $validated['manage_stock'] = $request->boolean('manage_stock');
        $validated['hide_price'] = $request->boolean('hide_price');

        // Handle dimensions
        if ($validated['length'] || $validated['width'] || $validated['height']) {
            $validated['dimensions'] = [
                'length' => $validated['length'] ?? 0,
                'width' => $validated['width'] ?? 0,
                'height' => $validated['height'] ?? 0,
            ];
        }
        unset($validated['length'], $validated['width'], $validated['height']);

        // Store in session
        Session::put('product_form_data.step3', $validated);

        return redirect()->route('admin.products.create.step4');
    }

    /**
     * Show Step 4 form (Images & Media)
     */
    public function createStep4()
    {
        $step1Data = Session::get('product_form_data.step1');
        $step2Data = Session::get('product_form_data.step2');
        $step3Data = Session::get('product_form_data.step3');

        if (!$step1Data || !$step2Data || !$step3Data) {
            return redirect()->route('admin.products.create')
                           ->with('error', 'Please complete previous steps first.');
        }

        return view('admin.products.create.step4', compact('step1Data', 'step2Data', 'step3Data'));
    }

    /**
     * Handle final form submission and create product
     */
    public function storeStep4(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'reviews_allowed' => 'boolean',
        ]);

        $validated['reviews_allowed'] = $request->boolean('reviews_allowed');

        // Get all session data
        $step1Data = Session::get('product_form_data.step1');
        $step2Data = Session::get('product_form_data.step2');
        $step3Data = Session::get('product_form_data.step3');

        if (!$step1Data || !$step2Data || !$step3Data) {
            return redirect()->route('admin.products.create')
                           ->with('error', 'Session expired. Please start over.');
        }

        // Merge all data
        $productData = array_merge($step1Data, $step2Data, $step3Data, $validated);

        // Handle featured image upload with processing
        if ($request->hasFile('featured_image')) {
            try {
                $productData['featured_image'] = $this->imageService->processProductImage($request->file('featured_image'));
                \Log::info('Product featured image processed successfully', ['path' => $productData['featured_image']]);
            } catch (\Exception $e) {
                \Log::error('Product featured image processing failed', ['error' => $e->getMessage()]);
                // Fallback to original storage method
                $productData['featured_image'] = $request->file('featured_image')->store('uploads/products', 'public');
            }
        }

        // Handle gallery images upload with processing
        if ($request->hasFile('gallery_images')) {
            $galleryImages = [];
            foreach ($request->file('gallery_images') as $image) {
                try {
                    $galleryImages[] = $this->imageService->processProductImage($image);
                    \Log::info('Product gallery image processed successfully');
                } catch (\Exception $e) {
                    \Log::error('Product gallery image processing failed', ['error' => $e->getMessage()]);
                    // Fallback to original storage method
                    $galleryImages[] = $image->store('uploads/products', 'public');
                }
            }
            $productData['gallery_images'] = $galleryImages;
        }

        // Generate slug and SKU if not provided
        if (empty($productData['slug'])) {
            $productData['slug'] = Product::generateSlug($productData['name']);
        }

        if (empty($productData['sku'])) {
            $productData['sku'] = Product::generateSku($productData['name']);
        }

        // Create the product
        $product = Product::create($productData);

        // Clear session data
        Session::forget('product_form_data');

        return redirect()->route('admin.products.show', $product)
                        ->with('success', 'Product created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product): View
    {
        $product->load(['category']);

        return view('admin.products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product): View
    {
        $categories = Category::active()
                            ->with('children')
                            ->orderBy('sort_order')
                            ->get();

        return view('admin.products.edit', compact('product', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'sku' => 'required|string|max:100|unique:products,sku,' . $product->id,
            'category_id' => 'required|exists:categories,id',
            'description' => 'nullable|string',
            'additional_information' => 'nullable|string',
            'technical_specifications' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'hide_price' => 'boolean',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|array',
            'dimensions.length' => 'nullable|numeric|min:0',
            'dimensions.width' => 'nullable|numeric|min:0',
            'dimensions.height' => 'nullable|numeric|min:0',
            'stock_quantity' => 'nullable|integer|min:0',
            'min_stock_level' => 'nullable|integer|min:0',
            'manage_stock' => 'boolean',
            'stock_status' => 'required|in:in_stock,out_of_stock,on_backorder',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'remove_gallery_images' => 'nullable|array',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_digital' => 'boolean',
            'is_virtual' => 'boolean',
            'reviews_allowed' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'meta_title' => 'nullable|string|max:60',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string',
            'tags' => 'nullable|string',
            'date_on_sale_from' => 'nullable|date',
            'date_on_sale_to' => 'nullable|date|after:date_on_sale_from',
        ]);

        // Handle boolean fields
        $validated['manage_stock'] = $request->boolean('manage_stock');
        $validated['hide_price'] = $request->boolean('hide_price');
        $validated['is_active'] = $request->boolean('is_active');
        $validated['is_featured'] = $request->boolean('is_featured');
        $validated['is_digital'] = $request->boolean('is_digital');
        $validated['is_virtual'] = $request->boolean('is_virtual');
        $validated['reviews_allowed'] = $request->boolean('reviews_allowed');

        // Handle tags
        if (isset($validated['tags'])) {
            $validated['tags'] = array_map('trim', explode(',', $validated['tags']));
        }

        // Handle featured image upload with processing
        if ($request->hasFile('featured_image')) {
            // Delete old featured image
            if ($product->featured_image) {
                $this->imageService->deleteOldImage($product->featured_image);
            }

            try {
                $validated['featured_image'] = $this->imageService->processProductImage($request->file('featured_image'));
                \Log::info('Product featured image updated and processed successfully', ['path' => $validated['featured_image']]);
            } catch (\Exception $e) {
                \Log::error('Product featured image processing failed during update', ['error' => $e->getMessage()]);
                // Fallback to original storage method
                $validated['featured_image'] = $request->file('featured_image')->store('uploads/products', 'public');
            }
        }

        // Handle gallery images
        $currentGalleryImages = $product->gallery_images ?? [];

        // Remove selected images
        if ($request->has('remove_gallery_images')) {
            foreach ($request->remove_gallery_images as $index) {
                if (isset($currentGalleryImages[$index])) {
                    $this->imageService->deleteOldImage($currentGalleryImages[$index]);
                    unset($currentGalleryImages[$index]);
                }
            }
            $currentGalleryImages = array_values($currentGalleryImages); // Re-index array
        }

        // Add new gallery images with processing
        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $image) {
                try {
                    $currentGalleryImages[] = $this->imageService->processProductImage($image);
                    \Log::info('Product gallery image processed successfully during update');
                } catch (\Exception $e) {
                    \Log::error('Product gallery image processing failed during update', ['error' => $e->getMessage()]);
                    // Fallback to original storage method
                    $currentGalleryImages[] = $image->store('uploads/products', 'public');
                }
            }
        }

        $validated['gallery_images'] = $currentGalleryImages;

        // Remove file upload fields from validated data
        unset($validated['remove_gallery_images']);

        $product->update($validated);

        return redirect()->route('admin.products.show', $product)
                        ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product): RedirectResponse
    {
        // Delete associated images
        if ($product->featured_image && Storage::disk('public')->exists($product->featured_image)) {
            Storage::disk('public')->delete($product->featured_image);
        }

        if ($product->gallery_images) {
            foreach ($product->gallery_images as $image) {
                if (Storage::disk('public')->exists($image)) {
                    Storage::disk('public')->delete($image);
                }
            }
        }

        $product->delete();

        return redirect()->route('admin.products.index')
                        ->with('success', 'Product deleted successfully.');
    }

    /**
     * Toggle product status
     */
    public function toggleStatus(Product $product): RedirectResponse
    {
        $product->update(['is_active' => !$product->is_active]);

        $status = $product->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
                        ->with('success', "Product {$status} successfully.");
    }

    /**
     * Duplicate a product
     */
    public function duplicate(Product $product): RedirectResponse
    {
        $newProduct = $product->replicate();

        // Modify duplicated product data
        $newProduct->name = $product->name . ' (Copy)';
        $newProduct->sku = Product::generateSku($newProduct->name);
        $newProduct->slug = Product::generateSlug($newProduct->name);
        $newProduct->is_active = false; // Deactivate copy by default
        $newProduct->sort_order = Product::max('sort_order') + 1;

        // Don't copy images - admin will need to upload new ones
        $newProduct->featured_image = null;
        $newProduct->gallery_images = null;

        $newProduct->save();

        return redirect()->route('admin.products.edit', $newProduct)
                        ->with('success', 'Product duplicated successfully. Please update the images and activate when ready.');
    }

    /**
     * Show product analytics
     */
    public function analytics(Product $product): View
    {
        // Calculate analytics data
        $analytics = [
            'views' => 0, // Would be tracked in a separate table
            'sales' => $product->total_sales,
            'revenue' => $product->total_sales * $product->getCurrentPrice(),
            'conversion_rate' => 0, // views > 0 ? ($product->total_sales / views) * 100 : 0
            'profit_margin' => $product->cost_price > 0 ?
                (($product->getCurrentPrice() - $product->cost_price) / $product->getCurrentPrice()) * 100 : 0,
            'stock_level' => $product->stock_quantity,
            'low_stock_alert' => $product->manage_stock && $product->stock_quantity <= $product->min_stock_level,
        ];

        // Get sales data for chart (mock data for now)
        $salesData = [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'data' => [0, 0, 0, 0, 0, $product->total_sales], // Would come from orders table
        ];

        return view('admin.products.analytics', compact('product', 'analytics', 'salesData'));
    }

    /**
     * Handle bulk actions
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete,feature,unfeature',
            'products' => 'required|array',
            'products.*' => 'exists:products,id',
        ]);

        $products = Product::whereIn('id', $validated['products']);
        $count = $products->count();

        switch ($validated['action']) {
            case 'activate':
                $products->update(['is_active' => true]);
                $message = "{$count} products activated successfully.";
                break;
            case 'deactivate':
                $products->update(['is_active' => false]);
                $message = "{$count} products deactivated successfully.";
                break;
            case 'feature':
                $products->update(['is_featured' => true]);
                $message = "{$count} products marked as featured successfully.";
                break;
            case 'unfeature':
                $products->update(['is_featured' => false]);
                $message = "{$count} products unmarked as featured successfully.";
                break;
            case 'delete':
                // Delete associated images
                foreach ($products->get() as $product) {
                    if ($product->featured_image && Storage::disk('public')->exists($product->featured_image)) {
                        Storage::disk('public')->delete($product->featured_image);
                    }
                    if ($product->gallery_images) {
                        foreach ($product->gallery_images as $image) {
                            if (Storage::disk('public')->exists($image)) {
                                Storage::disk('public')->delete($image);
                            }
                        }
                    }
                }
                $products->delete();
                $message = "{$count} products deleted successfully.";
                break;
        }

        return redirect()->route('admin.products.index')
                        ->with('success', $message);
    }
}
