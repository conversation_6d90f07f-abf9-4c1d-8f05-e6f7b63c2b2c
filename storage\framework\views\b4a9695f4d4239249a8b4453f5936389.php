<?php $__env->startSection('title', 'Create Support Ticket'); ?>
<?php $__env->startSection('page-title', 'Create Support Ticket'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('customer.support.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Tickets
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        New Support Ticket
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('customer.support.store')); ?>">
                        <?php echo csrf_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="subject" class="form-label">Subject *</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="subject" name="subject" value="<?php echo e(old('subject')); ?>" 
                                       placeholder="Brief description of your issue" required>
                                <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="priority" class="form-label">Priority *</label>
                                <select class="form-select <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="priority" name="priority" required>
                                    <option value="">Select Priority</option>
                                    <?php $__currentLoopData = $priorities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value); ?>" <?php echo e(old('priority') === $value ? 'selected' : ''); ?>>
                                            <?php echo e($label); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">Category *</label>
                                <select class="form-select <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value); ?>" <?php echo e(old('category') === $value ? 'selected' : ''); ?>>
                                            <?php echo e($label); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="order_number" class="form-label">Related Order (Optional)</label>
                                <select class="form-select <?php $__errorArgs = ['order_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="order_number" name="order_number">
                                    <option value="">Select an order (if applicable)</option>
                                    <?php $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($order->order_number); ?>" 
                                                <?php echo e(old('order_number') === $order->order_number || request('order_number') === $order->order_number ? 'selected' : ''); ?>>
                                            Order #<?php echo e($order->order_number); ?> - <?php echo e($order->created_at->format('M d, Y')); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['order_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="parcel_tracking" class="form-label">Related Parcel Tracking (Optional)</label>
                            <select class="form-select <?php $__errorArgs = ['parcel_tracking'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="parcel_tracking" name="parcel_tracking">
                                <option value="">Select a parcel (if applicable)</option>
                                <?php $__currentLoopData = $recentParcels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($parcel->tracking_number); ?>" 
                                            <?php echo e(old('parcel_tracking') === $parcel->tracking_number ? 'selected' : ''); ?>>
                                        <?php echo e($parcel->tracking_number); ?> - <?php echo e($parcel->created_at->format('M d, Y')); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['parcel_tracking'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="description" name="description" rows="6" 
                                      placeholder="Please provide detailed information about your issue or question..." required><?php echo e(old('description')); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">
                                Please be as specific as possible. Include any error messages, steps you've taken, 
                                and what you expected to happen.
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo e(route('customer.support.index')); ?>" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>
                                Submit Ticket
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Help & Tips -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Tips for Better Support
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">Be Specific</h6>
                        <p class="small text-muted">
                            Provide detailed information about your issue, including any error messages you've seen.
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Include Context</h6>
                        <p class="small text-muted">
                            Tell us what you were trying to do when the problem occurred and what you expected to happen.
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Choose the Right Priority</h6>
                        <p class="small text-muted">
                            <strong>Urgent:</strong> Service is down or critical business impact<br>
                            <strong>High:</strong> Important feature not working<br>
                            <strong>Medium:</strong> General questions or minor issues<br>
                            <strong>Low:</strong> Feature requests or suggestions
                        </p>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        Other Ways to Reach Us
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Phone Support</h6>
                        <p class="mb-1"><strong><?php echo e(\App\Models\SiteSetting::getValue('support_phone', '+****************')); ?></strong></p>
                        <small class="text-muted"><?php echo e(\App\Models\SiteSetting::getValue('support_hours', 'Mon-Fri: 8AM-6PM EST')); ?></small>
                    </div>

                    <div class="mb-3">
                        <h6>Email Support</h6>
                        <p class="mb-1"><strong><?php echo e(\App\Models\SiteSetting::getValue('support_email', '<EMAIL>')); ?></strong></p>
                        <small class="text-muted"><?php echo e(\App\Models\SiteSetting::getValue('support_response_time', 'Response within 24 hours')); ?></small>
                    </div>

                    <?php if(\App\Models\SiteSetting::getValue('live_chat_enabled', true)): ?>
                    <div class="mb-3">
                        <h6>Live Chat</h6>
                        <p class="mb-1">Available on our website</p>
                        <small class="text-muted"><?php echo e(\App\Models\SiteSetting::getValue('live_chat_hours', 'Mon-Fri: 9AM-5PM EST')); ?></small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- FAQ -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        Frequently Asked Questions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    How do I track my package?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <small>You can track your package using the tracking number provided in your order confirmation email. Visit our tracking page or use the "Track Package" feature in your dashboard.</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    What are your shipping rates?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <small>Shipping rates depend on package size, weight, destination, and service level. Use our quote calculator for accurate pricing or contact us for bulk shipping discounts.</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    How do I change my delivery address?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <small>Contact us immediately if you need to change a delivery address. Changes may be possible if the package hasn't been dispatched yet. Additional fees may apply.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Auto-fill description based on category selection
    document.getElementById('category').addEventListener('change', function() {
        const category = this.value;
        const descriptionField = document.getElementById('description');
        
        if (category && !descriptionField.value) {
            let template = '';
            
            switch(category) {
                case 'technical':
                    template = 'I am experiencing a technical issue with:\n\nSteps to reproduce:\n1. \n2. \n3. \n\nExpected result:\n\nActual result:\n\nBrowser/Device information:';
                    break;
                case 'billing':
                    template = 'I have a question about my billing:\n\nOrder/Invoice number:\n\nIssue description:\n\nAmount in question:';
                    break;
                case 'shipping':
                    template = 'I have an issue with my shipment:\n\nTracking number:\n\nExpected delivery date:\n\nIssue description:';
                    break;
                case 'product':
                    template = 'I have a question about a product:\n\nProduct name/SKU:\n\nQuestion/Issue:';
                    break;
                default:
                    template = 'Please describe your issue or question in detail:';
            }
            
            descriptionField.value = template;
        }
    });

    // Character counter for description
    const descriptionField = document.getElementById('description');
    const maxLength = 5000;
    
    // Create character counter
    const counter = document.createElement('div');
    counter.className = 'form-text text-end';
    counter.id = 'char-counter';
    descriptionField.parentNode.appendChild(counter);
    
    function updateCounter() {
        const remaining = maxLength - descriptionField.value.length;
        counter.textContent = `${descriptionField.value.length}/${maxLength} characters`;
        counter.className = remaining < 100 ? 'form-text text-end text-warning' : 'form-text text-end text-muted';
    }
    
    descriptionField.addEventListener('input', updateCounter);
    updateCounter(); // Initial count
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/customer/support/create.blade.php ENDPATH**/ ?>