<?php

namespace App\Http\Controllers;

use App\Models\BlogPost;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\View\View;

class BlogController extends Controller
{
    /**
     * Display a listing of blog posts
     */
    public function index(Request $request): View
    {
        $siteSettings = $this->getSiteSettings();
        
        $query = BlogPost::published()
            ->with('author')
            ->orderBy('published_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%")
                  ->orWhere('excerpt', 'like', "%{$searchTerm}%");
            });
        }

        // Tag filtering
        if ($request->filled('tag')) {
            $tag = $request->get('tag');
            $query->whereJsonContains('tags', $tag);
        }

        // Featured posts filter
        if ($request->filled('featured')) {
            $query->featured();
        }

        $posts = $query->paginate(12);
        
        // Get popular tags
        $popularTags = $this->getPopularTags();
        
        // Get featured posts for sidebar
        $featuredPosts = BlogPost::published()
            ->featured()
            ->limit(5)
            ->get();

        return view('frontend.blog.index', compact(
            'siteSettings', 
            'posts', 
            'popularTags', 
            'featuredPosts'
        ));
    }

    /**
     * Display the specified blog post
     */
    public function show(BlogPost $blogPost): View
    {
        // Check if post is published
        if ($blogPost->status !== 'published' || 
            ($blogPost->published_at && $blogPost->published_at->isFuture())) {
            abort(404);
        }

        $siteSettings = $this->getSiteSettings();
        
        // Increment view count
        $blogPost->incrementViewCount();
        
        // Get related posts
        $relatedPosts = $blogPost->getRelatedPosts(3);
        
        // Get next and previous posts
        $nextPost = BlogPost::published()
            ->where('published_at', '>', $blogPost->published_at)
            ->orderBy('published_at', 'asc')
            ->first();
            
        $previousPost = BlogPost::published()
            ->where('published_at', '<', $blogPost->published_at)
            ->orderBy('published_at', 'desc')
            ->first();

        return view('frontend.blog.show', compact(
            'siteSettings',
            'relatedPosts',
            'nextPost',
            'previousPost'
        ))->with('post', $blogPost);
    }

    /**
     * Get site settings
     */
    private function getSiteSettings(): array
    {
        return cache()->remember('site_settings', 3600, function () {
            return SiteSetting::pluck('value', 'key_name')->toArray();
        });
    }

    /**
     * Get popular tags
     */
    private function getPopularTags(): array
    {
        $posts = BlogPost::published()
            ->whereNotNull('tags')
            ->pluck('tags')
            ->flatten()
            ->countBy()
            ->sortDesc()
            ->take(20)
            ->keys()
            ->toArray();

        return $posts;
    }

    /**
     * RSS Feed
     */
    public function rss()
    {
        $posts = BlogPost::published()
            ->with('author')
            ->orderBy('published_at', 'desc')
            ->limit(20)
            ->get();

        $siteSettings = $this->getSiteSettings();

        return response()
            ->view('frontend.blog.rss', compact('posts', 'siteSettings'))
            ->header('Content-Type', 'application/rss+xml');
    }

    /**
     * Sitemap for blog posts
     */
    public function sitemap()
    {
        $posts = BlogPost::published()
            ->orderBy('updated_at', 'desc')
            ->get();

        return response()
            ->view('frontend.blog.sitemap', compact('posts'))
            ->header('Content-Type', 'application/xml');
    }
}
