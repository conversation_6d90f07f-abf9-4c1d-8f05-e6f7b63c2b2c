<?php $__env->startSection('title', 'Quote Management'); ?>
<?php $__env->startSection('page-title', 'Quote Management'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.quotes.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> New Quote
        </a>
        <button type="button" class="btn btn-outline-secondary" onclick="exportQuotes()">
            <i class="fas fa-download me-1"></i> Export
        </button>
        <button type="button" class="btn btn-outline-info" onclick="refreshQuotes()">
            <i class="fas fa-sync me-1"></i> Refresh
        </button>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Quote Statistics -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo e(number_format($stats['total_quotes'])); ?></h4>
                            <p class="mb-0">Total Quotes</p>
                        </div>
                        <div>
                            <i class="fas fa-quote-left fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo e(number_format($stats['pending_quotes'])); ?></h4>
                            <p class="mb-0">Pending</p>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo e(number_format($stats['active_quotes'])); ?></h4>
                            <p class="mb-0">Active</p>
                        </div>
                        <div>
                            <i class="fas fa-tasks fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo e(number_format($stats['accepted_quotes'])); ?></h4>
                            <p class="mb-0">Accepted</p>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">$<?php echo e(number_format($stats['total_value'], 0)); ?></h4>
                            <p class="mb-0">Total Value</p>
                        </div>
                        <div>
                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-secondary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">$<?php echo e(number_format($stats['avg_quote_value'], 0)); ?></h4>
                            <p class="mb-0">Avg Value</p>
                        </div>
                        <div>
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.quotes.index')); ?>" class="row g-3">
                <div class="col-md-2">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo e(request('search')); ?>" placeholder="Quote #, customer, company...">
                </div>

                <div class="col-md-2">
                    <label for="quote_type" class="form-label">Quote Type</label>
                    <select class="form-select" id="quote_type" name="quote_type">
                        <option value="">All Types</option>
                        <option value="shipping" <?php echo e(request('quote_type') === 'shipping' ? 'selected' : ''); ?>>Shipping</option>
                        <option value="product" <?php echo e(request('quote_type') === 'product' ? 'selected' : ''); ?>>Product</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($value); ?>" <?php echo e(request('status') === $value ? 'selected' : ''); ?>>
                                <?php echo e($label); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="service_type" class="form-label">Service</label>
                    <select class="form-select" id="service_type" name="service_type">
                        <option value="">All Services</option>
                        <?php $__currentLoopData = $serviceTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($value); ?>" <?php echo e(request('service_type') === $value ? 'selected' : ''); ?>>
                                <?php echo e($label); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <option value="product_inquiry" <?php echo e(request('service_type') === 'product_inquiry' ? 'selected' : ''); ?>>
                            Product Inquiry
                        </option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="priority" class="form-label">Priority</label>
                    <select class="form-select" id="priority" name="priority">
                        <option value="">All Priorities</option>
                        <?php $__currentLoopData = $priorities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($value); ?>" <?php echo e(request('priority') === $value ? 'selected' : ''); ?>>
                                <?php echo e($label); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="assigned_to" class="form-label">Assigned To</label>
                    <select class="form-select" id="assigned_to" name="assigned_to">
                        <option value="">All Admins</option>
                        <?php $__currentLoopData = $admins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $admin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($admin->id); ?>" <?php echo e(request('assigned_to') == $admin->id ? 'selected' : ''); ?>>
                                <?php echo e($admin->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quotes Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-quote-left me-2"></i>
                Quotes (<?php echo e($quotes->total()); ?>)
            </h5>
            
            <?php if($quotes->count() > 0): ?>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                            data-bs-toggle="dropdown">
                        Bulk Actions
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="showBulkAssignModal()">
                            <i class="fas fa-user-plus me-2"></i> Assign Selected
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showBulkStatusModal()">
                            <i class="fas fa-edit me-2"></i> Update Status
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                            <i class="fas fa-trash me-2"></i> Delete Selected
                        </a></li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="card-body">
            <?php if($quotes->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Quote #</th>
                                <th>Customer</th>
                                <th>Type & Service</th>
                                <th>Details</th>
                                <th>Value</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Assigned</th>
                                <th>Created</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $quotes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quote): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input quote-checkbox" 
                                               value="<?php echo e($quote->id); ?>">
                                    </td>
                                    <td>
                                        <strong><?php echo e($quote->quote_number); ?></strong>
                                        <?php if($quote->isExpired()): ?>
                                            <br><small class="text-danger">Expired</small>
                                        <?php elseif($quote->expires_at): ?>
                                            <br><small class="text-muted">Expires <?php echo e($quote->expires_at->diffForHumans()); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($quote->customer_name); ?></strong>
                                            <?php if($quote->company_name): ?>
                                                <br><small class="text-muted"><?php echo e($quote->company_name); ?></small>
                                            <?php endif; ?>
                                            <br><a href="mailto:<?php echo e($quote->customer_email); ?>"><?php echo e($quote->customer_email); ?></a>
                                            <?php if($quote->customer_phone): ?>
                                                <br><small class="text-muted"><?php echo e($quote->customer_phone); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="badge bg-<?php echo e($quote->quote_type === 'product' ? 'success' : 'primary'); ?>">
                                                <?php echo e(ucwords($quote->quote_type)); ?>

                                            </span>
                                            <?php if($quote->quote_source): ?>
                                                <span class="badge bg-secondary"><?php echo e(ucwords(str_replace('_', ' ', $quote->quote_source))); ?></span>
                                            <?php endif; ?>
                                            <br>
                                            <span class="badge bg-info"><?php echo e($quote->formatted_service_type); ?></span>
                                            <?php if($quote->isShippingQuote() && $quote->package_count > 1): ?>
                                                <br><small class="text-muted"><?php echo e($quote->package_count); ?> packages</small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($quote->isShippingQuote()): ?>
                                            <div>
                                                <strong>From:</strong> <?php echo e($quote->origin_city ?? 'N/A'); ?>, <?php echo e($quote->origin_country ?? 'N/A'); ?>

                                                <br><strong>To:</strong> <?php echo e($quote->destination_city ?? 'N/A'); ?>, <?php echo e($quote->destination_country ?? 'N/A'); ?>

                                                <?php if($quote->total_weight): ?>
                                                    <br><small class="text-muted">Weight: <?php echo e($quote->total_weight); ?> <?php echo e($quote->weight_unit); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        <?php elseif($quote->isProductQuote()): ?>
                                            <div>
                                                <?php if($quote->products): ?>
                                                    <strong><?php echo e($quote->getProductsCount()); ?> Product(s)</strong>
                                                    <br><small class="text-muted">Total Value: $<?php echo e(number_format($quote->products_total, 2)); ?></small>
                                                    <?php if($quote->getProductsWithDetails()->count() > 0): ?>
                                                        <?php $firstProduct = $quote->getProductsWithDetails()->first(); ?>
                                                        <br><small class="text-muted"><?php echo e($firstProduct['product']->name ?? 'Product'); ?>

                                                        <?php if($quote->getProductsCount() > 1): ?>
                                                            + <?php echo e($quote->getProductsCount() - 1); ?> more
                                                        <?php endif; ?>
                                                        </small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Product inquiry</span>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Details not available</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($quote->final_price): ?>
                                            <strong>$<?php echo e(number_format($quote->final_price, 2)); ?></strong>
                                            <?php if($quote->discount_amount > 0): ?>
                                                <br><small class="text-success">-$<?php echo e(number_format($quote->discount_amount, 2)); ?> discount</small>
                                            <?php endif; ?>
                                        <?php elseif($quote->quoted_price): ?>
                                            <strong>$<?php echo e(number_format($quote->quoted_price, 2)); ?></strong>
                                        <?php else: ?>
                                            <span class="text-muted">Not quoted</span>
                                            <?php if($quote->isProductQuote() && $quote->products_total): ?>
                                                <br><small class="text-info">Products: $<?php echo e(number_format($quote->products_total, 2)); ?></small>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo e($quote->priority_badge_color); ?>">
                                            <?php echo e($quote->formatted_priority); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo e($quote->status_badge_color); ?>">
                                            <?php echo e($quote->formatted_status); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php if($quote->assignedTo): ?>
                                            <span class="badge bg-secondary"><?php echo e($quote->assignedTo->name); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">Unassigned</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e($quote->created_at->format('M d, Y')); ?>

                                        <br><small class="text-muted"><?php echo e($quote->created_at->diffForHumans()); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.quotes.show', $quote)); ?>" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if($quote->canBeQuoted()): ?>
                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                        onclick="showQuoteModal(<?php echo e($quote->id); ?>)" title="Provide Quote">
                                                    <i class="fas fa-dollar-sign"></i>
                                                </button>
                                            <?php endif; ?>
                                            <a href="<?php echo e(route('admin.quotes.edit', $quote)); ?>" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-3">
                    <?php echo e($quotes->links('pagination.admin')); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-quote-left fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No quotes found</h5>
                    <p class="text-muted">Start by creating your first quote or adjust your filters.</p>
                    <a href="<?php echo e(route('admin.quotes.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i> Create First Quote
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Provide Quote Modal -->
    <div class="modal fade" id="quoteModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Provide Quote</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="quoteForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="quoted_price" class="form-label">Quoted Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="quoted_price" name="quoted_price" 
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="discount_amount" class="form-label">Discount Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                           step="0.01" min="0" value="0">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="expires_at" class="form-label">Quote Expires At *</label>
                            <input type="datetime-local" class="form-control" id="expires_at" name="expires_at" required>
                        </div>

                        <div class="mb-3">
                            <label for="pricing_breakdown" class="form-label">Pricing Breakdown</label>
                            <textarea class="form-control" id="pricing_breakdown" name="pricing_breakdown" rows="4"
                                      placeholder="Base shipping: $X&#10;Fuel surcharge: $X&#10;Insurance: $X&#10;Total: $X"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">Admin Notes</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                      placeholder="Internal notes about this quote..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitQuote()">Provide Quote</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    let currentQuoteId = null;

    // Select all functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.quote-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Show quote modal
    function showQuoteModal(quoteId) {
        currentQuoteId = quoteId;

        // Set default expiry date (7 days from now)
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 7);
        document.getElementById('expires_at').value = expiryDate.toISOString().slice(0, 16);

        new bootstrap.Modal(document.getElementById('quoteModal')).show();
    }

    // Submit quote
    function submitQuote() {
        const form = document.getElementById('quoteForm');
        const formData = new FormData(form);

        fetch(`/admin/quotes/${currentQuoteId}/provide-quote`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(Object.fromEntries(formData))
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('quoteModal')).hide();
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    }

    // Bulk actions
    function showBulkAssignModal() {
        const selectedQuotes = getSelectedQuotes();
        if (selectedQuotes.length === 0) {
            alert('Please select at least one quote.');
            return;
        }

        const adminId = prompt('Enter admin ID to assign to:');
        if (adminId) {
            bulkAction('assign', { assigned_to: adminId });
        }
    }

    function showBulkStatusModal() {
        const selectedQuotes = getSelectedQuotes();
        if (selectedQuotes.length === 0) {
            alert('Please select at least one quote.');
            return;
        }

        const status = prompt('Enter new status (pending, reviewing, quoted, accepted, rejected, expired):');
        if (status) {
            bulkAction('update_status', { status: status });
        }
    }

    function bulkAction(action, additionalData = {}) {
        const selectedQuotes = getSelectedQuotes();

        if (selectedQuotes.length === 0) {
            alert('Please select at least one quote.');
            return;
        }

        const actionText = action === 'delete' ? 'delete' : action.replace('_', ' ');
        if (confirm(`Are you sure you want to ${actionText} ${selectedQuotes.length} quote(s)?`)) {
            fetch('<?php echo e(route("admin.quotes.bulk-action")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({
                    quote_ids: selectedQuotes,
                    action: action,
                    ...additionalData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        }
    }

    function getSelectedQuotes() {
        return Array.from(document.querySelectorAll('.quote-checkbox:checked'))
                   .map(cb => cb.value);
    }

    // Utility functions
    function refreshQuotes() {
        location.reload();
    }

    function exportQuotes() {
        alert('Export functionality coming soon!');
    }

    // Auto-calculate final price
    document.getElementById('quoted_price').addEventListener('input', calculateFinalPrice);
    document.getElementById('discount_amount').addEventListener('input', calculateFinalPrice);

    function calculateFinalPrice() {
        const quotedPrice = parseFloat(document.getElementById('quoted_price').value) || 0;
        const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
        const finalPrice = quotedPrice - discountAmount;

        // You can display the final price somewhere if needed
        console.log('Final Price:', finalPrice);
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/quotes/index.blade.php ENDPATH**/ ?>