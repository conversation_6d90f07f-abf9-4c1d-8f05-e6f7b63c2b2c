<?php $__env->startSection('title', 'Manage Parcels'); ?>
<?php $__env->startSection('page-title', 'Parcels Management'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.parcels.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> New Parcel
        </a>
        <button type="button" class="btn btn-outline-info" id="bulkActionsBtn" style="display: none;" data-bs-toggle="dropdown">
            <i class="fas fa-tasks me-1"></i> Bulk Actions <span class="badge bg-white text-info" id="selectedCount">0</span>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="showBulkModal('update_status')">
                <i class="fas fa-edit me-2"></i> Update Status
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="showBulkModal('mark_paid')">
                <i class="fas fa-check me-2"></i> Mark as Paid
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="showBulkModal('mark_unpaid')">
                <i class="fas fa-times me-2"></i> Mark as Unpaid
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-danger" href="#" onclick="showBulkModal('delete')">
                <i class="fas fa-trash me-2"></i> Delete Selected
            </a></li>
        </ul>
        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
            <i class="fas fa-print me-1"></i> Print
        </button>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.parcels.index')); ?>">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo e(request('search')); ?>" 
                               placeholder="Tracking number, sender, recipient...">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($status); ?>" <?php echo e(request('status') === $status ? 'selected' : ''); ?>>
                                    <?php echo e(ucfirst(str_replace('_', ' ', $status))); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="carrier_id" class="form-label">Carrier</label>
                        <select class="form-select" id="carrier_id" name="carrier_id">
                            <option value="">All Carriers</option>
                            <?php $__currentLoopData = $carriers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $carrier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($carrier->id); ?>" <?php echo e(request('carrier_id') == $carrier->id ? 'selected' : ''); ?>>
                                    <?php echo e($carrier->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> Filter
                            </button>
                            <a href="<?php echo e(route('admin.parcels.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Parcels Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                Parcels (<?php echo e($parcels->total()); ?> total)
            </h6>
            <div class="text-muted small">
                Showing <?php echo e($parcels->firstItem() ?? 0); ?> to <?php echo e($parcels->lastItem() ?? 0); ?> of <?php echo e($parcels->total()); ?> results
            </div>
        </div>
        <div class="card-body">
            <?php if($parcels->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                </th>
                                <th>Tracking Number</th>
                                <th>Customer</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Carrier</th>
                                <th>Weight</th>
                                <th>Cost</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $parcels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input parcel-checkbox" value="<?php echo e($parcel->id); ?>">
                                    </td>
                                    <td>
                                        <strong><?php echo e($parcel->tracking_number); ?></strong><br>
                                        <small class="text-muted">
                                            <?php echo e($parcel->sender_city); ?> → <?php echo e($parcel->recipient_city); ?>

                                        </small>
                                    </td>
                                    <td>
                                        <strong><?php echo e($parcel->recipient_name); ?></strong><br>
                                        <small class="text-muted"><?php echo e($parcel->recipient_email); ?></small>
                                    </td>
                                    <td>
                                        <?php echo e(Str::limit($parcel->description, 40)); ?>

                                        <?php if($parcel->service_type): ?>
                                            <br><small class="text-muted"><?php echo e(ucfirst($parcel->service_type)); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo e($parcel->getStatusBadgeColor()); ?>">
                                            <?php echo e($parcel->getFormattedStatus()); ?>

                                        </span>
                                        <?php if($parcel->is_paid): ?>
                                            <br><small class="text-success"><i class="fas fa-check"></i> Paid</small>
                                        <?php else: ?>
                                            <br><small class="text-warning"><i class="fas fa-clock"></i> Unpaid</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo e($parcel->carrier->name); ?></strong><br>
                                        <small class="text-muted"><?php echo e($parcel->carrier->code); ?></small>
                                    </td>
                                    <td>
                                        <?php if($parcel->weight): ?>
                                            <?php echo e($parcel->weight); ?> kg
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo \App\Helpers\CurrencyHelper::format($parcel->total_cost); ?></strong>
                                        <?php if($parcel->declared_value): ?>
                                            <br><small class="text-muted">Value: <?php echo \App\Helpers\CurrencyHelper::format($parcel->declared_value); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e($parcel->created_at->format('M d, Y')); ?><br>
                                        <small class="text-muted"><?php echo e($parcel->created_at->format('h:i A')); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.parcels.show', $parcel)); ?>" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.parcels.edit', $parcel)); ?>" 
                                               class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    title="Delete" onclick="confirmDelete('<?php echo e($parcel->tracking_number); ?>', '<?php echo e(route('admin.parcels.destroy', $parcel)); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    <?php echo e($parcels->links('pagination.admin')); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-box fa-4x text-muted mb-4"></i>
                    <h5 class="text-muted">No parcels found</h5>
                    <p class="text-muted">
                        <?php if(request()->hasAny(['search', 'status', 'carrier_id'])): ?>
                            Try adjusting your search criteria or <a href="<?php echo e(route('admin.parcels.index')); ?>">clear filters</a>.
                        <?php else: ?>
                            Get started by creating your first parcel.
                        <?php endif; ?>
                    </p>
                    <a href="<?php echo e(route('admin.parcels.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Create First Parcel
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete parcel <strong id="deleteTrackingNumber"></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">Delete Parcel</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Operations Modal -->
    <div class="modal fade" id="bulkModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkModalTitle">Bulk Operation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="bulkForm" method="POST" action="<?php echo e(route('admin.parcels.bulk-update')); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div id="bulkModalContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                        <div class="mt-3">
                            <strong>Selected Parcels:</strong>
                            <div id="selectedParcelsPreview" class="mt-2"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="bulkSubmitBtn">Apply Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function confirmDelete(trackingNumber, deleteUrl) {
        document.getElementById('deleteTrackingNumber').textContent = trackingNumber;
        document.getElementById('deleteForm').action = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    // Auto-submit form on filter change
    document.getElementById('status').addEventListener('change', function() {
        this.form.submit();
    });

    document.getElementById('carrier_id').addEventListener('change', function() {
        this.form.submit();
    });

    // Bulk operations functionality
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const parcelCheckboxes = document.querySelectorAll('.parcel-checkbox');
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        const selectedCount = document.getElementById('selectedCount');

        // Select all functionality
        selectAllCheckbox.addEventListener('change', function() {
            parcelCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsVisibility();
        });

        // Individual checkbox change
        parcelCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateBulkActionsVisibility();

                // Update select all checkbox state
                const checkedCount = document.querySelectorAll('.parcel-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === parcelCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < parcelCheckboxes.length;
            });
        });

        function updateBulkActionsVisibility() {
            const checkedBoxes = document.querySelectorAll('.parcel-checkbox:checked');
            const count = checkedBoxes.length;

            if (count > 0) {
                bulkActionsBtn.style.display = 'inline-block';
                selectedCount.textContent = count;
            } else {
                bulkActionsBtn.style.display = 'none';
            }
        }
    });

    function showBulkModal(action) {
        const checkedBoxes = document.querySelectorAll('.parcel-checkbox:checked');
        const selectedIds = Array.from(checkedBoxes).map(cb => cb.value);

        if (selectedIds.length === 0) {
            alert('Please select at least one parcel.');
            return;
        }

        const modal = new bootstrap.Modal(document.getElementById('bulkModal'));
        const modalTitle = document.getElementById('bulkModalTitle');
        const modalContent = document.getElementById('bulkModalContent');
        const submitBtn = document.getElementById('bulkSubmitBtn');
        const form = document.getElementById('bulkForm');

        // Clear previous inputs
        const existingInputs = form.querySelectorAll('input[name="parcel_ids[]"], input[name="bulk_action"], input[name="new_status"]');
        existingInputs.forEach(input => input.remove());

        // Add selected parcel IDs
        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'parcel_ids[]';
            input.value = id;
            form.appendChild(input);
        });

        // Add bulk action
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'bulk_action';
        actionInput.value = action;
        form.appendChild(actionInput);

        // Configure modal based on action
        switch (action) {
            case 'update_status':
                modalTitle.textContent = 'Update Status';
                modalContent.innerHTML = `
                    <div class="mb-3">
                        <label for="new_status" class="form-label">New Status</label>
                        <select class="form-select" name="new_status" required>
                            <option value="">Select Status</option>
                            <option value="pending">Pending</option>
                            <option value="picked_up">Picked Up</option>
                            <option value="in_transit">In Transit</option>
                            <option value="out_for_delivery">Out for Delivery</option>
                            <option value="delivered">Delivered</option>
                            <option value="exception">Exception</option>
                            <option value="returned">Returned</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This will update the status for ${selectedIds.length} selected parcel(s) and create tracking events.
                    </div>
                `;
                submitBtn.textContent = 'Update Status';
                submitBtn.className = 'btn btn-primary';
                break;

            case 'mark_paid':
                modalTitle.textContent = 'Mark as Paid';
                modalContent.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        This will mark ${selectedIds.length} selected parcel(s) as paid.
                    </div>
                `;
                submitBtn.textContent = 'Mark as Paid';
                submitBtn.className = 'btn btn-success';
                break;

            case 'mark_unpaid':
                modalTitle.textContent = 'Mark as Unpaid';
                modalContent.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This will mark ${selectedIds.length} selected parcel(s) as unpaid.
                    </div>
                `;
                submitBtn.textContent = 'Mark as Unpaid';
                submitBtn.className = 'btn btn-warning';
                break;

            case 'delete':
                modalTitle.textContent = 'Delete Parcels';
                modalContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will permanently delete ${selectedIds.length} selected parcel(s) and all their tracking events.
                        This action cannot be undone.
                    </div>
                `;
                submitBtn.textContent = 'Delete Parcels';
                submitBtn.className = 'btn btn-danger';
                break;
        }

        // Show selected parcels preview
        const selectedParcelsPreview = document.getElementById('selectedParcelsPreview');
        const selectedTrackingNumbers = Array.from(checkedBoxes).map(cb => {
            const row = cb.closest('tr');
            const trackingCell = row.querySelector('td:nth-child(2) strong');
            return trackingCell ? trackingCell.textContent : 'Unknown';
        });

        selectedParcelsPreview.innerHTML = selectedTrackingNumbers.map(tn =>
            `<span class="badge bg-secondary me-1">${tn}</span>`
        ).join('');

        modal.show();
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/parcels/index.blade.php ENDPATH**/ ?>