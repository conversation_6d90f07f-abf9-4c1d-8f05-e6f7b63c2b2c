<?php $__env->startSection('title', 'My Parcels'); ?>
<?php $__env->startSection('page-title', 'My Parcels'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('customer.dashboard')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
        <a href="<?php echo e(route('customer.track')); ?>" class="btn btn-outline-primary">
            <i class="fas fa-search-location me-1"></i> Track Package
        </a>
        <a href="#" class="btn btn-primary" onclick="alert('Coming soon!')">
            <i class="fas fa-plus me-1"></i> New Shipment
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('customer.parcels')); ?>" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo e(request('search')); ?>" placeholder="Tracking number, description...">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($value); ?>" <?php echo e(request('status') == $value ? 'selected' : ''); ?>>
                                <?php echo e($label); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="carrier" class="form-label">Carrier</label>
                    <select class="form-select" id="carrier" name="carrier">
                        <option value="">All Carriers</option>
                        <?php $__currentLoopData = $carriers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $carrier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($carrier->id); ?>" <?php echo e(request('carrier') == $carrier->id ? 'selected' : ''); ?>>
                                <?php echo e($carrier->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo e(request('date_from')); ?>">
                </div>
                
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo e(request('date_to')); ?>">
                </div>
                
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
            
            <?php if(request()->hasAny(['search', 'status', 'carrier', 'date_from', 'date_to'])): ?>
                <div class="mt-3">
                    <a href="<?php echo e(route('customer.parcels')); ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i> Clear Filters
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Parcels List -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    Parcels (<?php echo e($parcels->total()); ?>)
                </h5>
                <div class="text-muted">
                    Showing <?php echo e($parcels->firstItem() ?? 0); ?> to <?php echo e($parcels->lastItem() ?? 0); ?> of <?php echo e($parcels->total()); ?> results
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if($parcels->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Tracking Number</th>
                                <th>Description</th>
                                <th>Recipient</th>
                                <th>Carrier</th>
                                <th>Status</th>
                                <th>Cost</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $parcels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <strong><?php echo e($parcel->tracking_number); ?></strong>
                                        <?php if($parcel->trackingEvents->count() > 0): ?>
                                            <br><small class="text-muted">
                                                Last update: <?php echo e($parcel->trackingEvents->first()->event_date->diffForHumans()); ?>

                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e(Str::limit($parcel->description, 40)); ?>

                                        <?php if($parcel->weight): ?>
                                            <br><small class="text-muted"><?php echo e($parcel->weight); ?>kg</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e($parcel->recipient_name); ?>

                                        <?php if($parcel->recipient_city): ?>
                                            <br><small class="text-muted"><?php echo e($parcel->recipient_city); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($parcel->carrier): ?>
                                            <span class="badge bg-info"><?php echo e($parcel->carrier->name); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">Not assigned</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                            $statusColors = [
                                                'pending' => 'secondary',
                                                'picked_up' => 'info',
                                                'in_transit' => 'warning',
                                                'out_for_delivery' => 'primary',
                                                'delivered' => 'success',
                                                'exception' => 'danger',
                                                'returned' => 'dark'
                                            ];
                                        ?>
                                        <span class="badge bg-<?php echo e($statusColors[$parcel->status] ?? 'secondary'); ?>">
                                            <?php echo e(ucwords(str_replace('_', ' ', $parcel->status))); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php if($parcel->total_cost): ?>
                                            <?php echo \App\Helpers\CurrencyHelper::format($parcel->total_cost); ?>
                                            <?php if(!$parcel->is_paid): ?>
                                                <br><small class="text-danger">Unpaid</small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">TBD</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e($parcel->created_at->format('M d, Y')); ?>

                                        <br><small class="text-muted"><?php echo e($parcel->created_at->diffForHumans()); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('customer.parcels.show', $parcel)); ?>" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if(!$parcel->is_paid && $parcel->total_cost): ?>
                                                <button class="btn btn-sm btn-outline-success" 
                                                        onclick="alert('Payment feature coming soon!')" title="Pay Now">
                                                    <i class="fas fa-credit-card"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($parcels->withQueryString()->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No parcels found</h5>
                    <?php if(request()->hasAny(['search', 'status', 'carrier', 'date_from', 'date_to'])): ?>
                        <p class="text-muted">Try adjusting your filters or search terms.</p>
                        <a href="<?php echo e(route('customer.parcels')); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>
                            Clear Filters
                        </a>
                    <?php else: ?>
                        <p class="text-muted">Your shipping history will appear here once you start using our services.</p>
                        <a href="#" class="btn btn-primary" onclick="alert('Coming soon!')">
                            <i class="fas fa-plus me-2"></i>
                            Create Your First Shipment
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Summary Cards -->
    <?php if($parcels->count() > 0): ?>
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary"><?php echo e($parcels->total()); ?></h5>
                        <p class="card-text text-muted">Total Parcels</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning">
                            <?php echo e($parcels->where('status', 'in_transit')->count() + $parcels->where('status', 'picked_up')->count()); ?>

                        </h5>
                        <p class="card-text text-muted">In Transit</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success"><?php echo e($parcels->where('status', 'delivered')->count()); ?></h5>
                        <p class="card-text text-muted">Delivered</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">
                            <?php echo \App\Helpers\CurrencyHelper::format($parcels->where('is_paid', true)->sum('total_cost')); ?>
                        </h5>
                        <p class="card-text text-muted">Total Paid</p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Auto-submit form when filters change
    document.querySelectorAll('#status, #carrier').forEach(function(element) {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Clear individual filters
    function clearFilter(filterName) {
        const input = document.querySelector(`[name="${filterName}"]`);
        if (input) {
            input.value = '';
            input.form.submit();
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/customer/parcels.blade.php ENDPATH**/ ?>