<?php $__env->startSection('title', 'User Management'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">User Management</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Users</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Add New User
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-2 col-md-4 mb-3">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Users</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['total_users'])); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-3">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Admins</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['admin_users'])); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-3">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Staff</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['staff_users'])); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-3">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Customers</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['customer_users'])); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-3">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['active_users'])); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-3">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Inactive</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['inactive_users'])); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Filters & Search</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.users.index')); ?>">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo e(request('search')); ?>" placeholder="Name, email, or phone...">
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">All Roles</option>
                                <option value="admin" <?php echo e(request('role') === 'admin' ? 'selected' : ''); ?>>Admin</option>
                                <option value="staff" <?php echo e(request('role') === 'staff' ? 'selected' : ''); ?>>Staff</option>
                                <option value="customer" <?php echo e(request('role') === 'customer' ? 'selected' : ''); ?>>Customer</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                                <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo e(request('date_from')); ?>">
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo e(request('date_to')); ?>">
                        </div>
                        
                        <div class="col-md-1 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <?php if(request()->hasAny(['search', 'role', 'status', 'date_from', 'date_to'])): ?>
                        <div class="row">
                            <div class="col-12">
                                <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-times me-1"></i> Clear Filters
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Users List</h6>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="toggleBulkActions()">
                        <i class="fas fa-tasks me-1"></i> Bulk Actions
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Bulk Actions Bar -->
                <div id="bulkActionsBar" class="alert alert-info" style="display: none;">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <span id="selectedCount">0</span> users selected
                        </div>
                        <div class="col-md-6 text-end">
                            <select id="bulkAction" class="form-select form-select-sm d-inline-block w-auto me-2">
                                <option value="">Choose Action...</option>
                                <option value="activate">Activate</option>
                                <option value="deactivate">Deactivate</option>
                                <option value="change_role">Change Role</option>
                                <option value="delete">Delete</option>
                            </select>
                            <select id="bulkRole" class="form-select form-select-sm d-inline-block w-auto me-2" style="display: none;">
                                <option value="admin">Admin</option>
                                <option value="staff">Staff</option>
                                <option value="customer">Customer</option>
                            </select>
                            <button class="btn btn-sm btn-primary" onclick="executeBulkAction()">Execute</button>
                            <button class="btn btn-sm btn-secondary" onclick="clearSelection()">Clear</button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="usersTable">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>User</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input user-checkbox" 
                                               value="<?php echo e($user->id); ?>" <?php echo e($user->id === auth()->id() ? 'disabled' : ''); ?>>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-3">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($user->name); ?></h6>
                                                <small class="text-muted"><?php echo e($user->email); ?></small>
                                                <?php if($user->phone): ?>
                                                    <br><small class="text-muted"><?php echo e($user->phone); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <select class="form-select form-select-sm role-select" 
                                                data-user-id="<?php echo e($user->id); ?>" 
                                                <?php echo e($user->id === auth()->id() ? 'disabled' : ''); ?>>
                                            <option value="admin" <?php echo e($user->role === 'admin' ? 'selected' : ''); ?>>Admin</option>
                                            <option value="staff" <?php echo e($user->role === 'staff' ? 'selected' : ''); ?>>Staff</option>
                                            <option value="customer" <?php echo e($user->role === 'customer' ? 'selected' : ''); ?>>Customer</option>
                                        </select>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox" 
                                                   data-user-id="<?php echo e($user->id); ?>" 
                                                   <?php echo e($user->is_active ? 'checked' : ''); ?>

                                                   <?php echo e($user->id === auth()->id() ? 'disabled' : ''); ?>>
                                            <label class="form-check-label">
                                                <?php echo e($user->is_active ? 'Active' : 'Inactive'); ?>

                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <small><?php echo e($user->created_at->format('M d, Y')); ?></small>
                                        <br><small class="text-muted"><?php echo e($user->created_at->diffForHumans()); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.users.show', $user)); ?>" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.users.edit', $user)); ?>" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if($user->id !== auth()->id()): ?>
                                                <button class="btn btn-sm btn-outline-warning" 
                                                        onclick="resetPassword(<?php echo e($user->id); ?>)" title="Reset Password">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteUser(<?php echo e($user->id); ?>)" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No users found matching your criteria.</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($users->hasPages()): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($users->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reset User Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="resetPasswordForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="newPassword" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirmPassword" name="password_confirmation" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Reset Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    let selectedUsers = [];
    let currentUserId = null;

    // Toggle bulk actions bar
    function toggleBulkActions() {
        const bar = document.getElementById('bulkActionsBar');
        bar.style.display = bar.style.display === 'none' ? 'block' : 'none';
    }

    // Select all checkbox functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.user-checkbox:not([disabled])');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedUsers();
    });

    // Individual checkbox functionality
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedUsers);
    });

    // Update selected users array
    function updateSelectedUsers() {
        selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
        document.getElementById('selectedCount').textContent = selectedUsers.length;

        // Show/hide bulk actions bar based on selection
        const bar = document.getElementById('bulkActionsBar');
        if (selectedUsers.length > 0) {
            bar.style.display = 'block';
        }
    }

    // Clear selection
    function clearSelection() {
        document.querySelectorAll('.user-checkbox').forEach(cb => cb.checked = false);
        document.getElementById('selectAll').checked = false;
        selectedUsers = [];
        document.getElementById('selectedCount').textContent = '0';
        document.getElementById('bulkActionsBar').style.display = 'none';
    }

    // Show/hide role selector for bulk actions
    document.getElementById('bulkAction').addEventListener('change', function() {
        const roleSelect = document.getElementById('bulkRole');
        roleSelect.style.display = this.value === 'change_role' ? 'inline-block' : 'none';
    });

    // Execute bulk action
    function executeBulkAction() {
        const action = document.getElementById('bulkAction').value;
        const role = document.getElementById('bulkRole').value;

        if (!action) {
            alert('Please select an action');
            return;
        }

        if (selectedUsers.length === 0) {
            alert('Please select users');
            return;
        }

        if (action === 'delete' && !confirm('Are you sure you want to delete the selected users?')) {
            return;
        }

        const data = {
            user_ids: selectedUsers,
            action: action,
            _token: '<?php echo e(csrf_token()); ?>'
        };

        if (action === 'change_role') {
            data.role = role;
        }

        fetch('<?php echo e(route("admin.users.bulk-action")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }

    // Status toggle functionality
    document.querySelectorAll('.status-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const userId = this.dataset.userId;

            fetch(`/admin/users/${userId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const label = this.nextElementSibling;
                    label.textContent = data.status ? 'Active' : 'Inactive';
                } else {
                    alert('Error: ' + data.message);
                    this.checked = !this.checked; // Revert toggle
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.checked = !this.checked; // Revert toggle
            });
        });
    });

    // Role change functionality
    document.querySelectorAll('.role-select').forEach(select => {
        select.addEventListener('change', function() {
            const userId = this.dataset.userId;
            const newRole = this.value;
            const oldRole = this.dataset.oldRole || this.querySelector('option[selected]')?.value;

            if (!confirm(`Change user role to ${newRole}?`)) {
                this.value = oldRole; // Revert selection
                return;
            }

            fetch(`/admin/users/${userId}/update-role`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({ role: newRole })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.dataset.oldRole = newRole;
                    alert(data.message);
                } else {
                    alert('Error: ' + data.message);
                    this.value = oldRole; // Revert selection
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.value = oldRole; // Revert selection
            });
        });
    });

    // Reset password functionality
    function resetPassword(userId) {
        currentUserId = userId;
        const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
        modal.show();
    }

    // Reset password form submission
    document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const password = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (password !== confirmPassword) {
            alert('Passwords do not match');
            return;
        }

        fetch(`/admin/users/${currentUserId}/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({
                password: password,
                password_confirmation: confirmPassword
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                const modal = bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal'));
                modal.hide();
                this.reset();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    });

    // Delete user functionality
    function deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            return;
        }

        fetch(`/admin/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error deleting user');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/users/index.blade.php ENDPATH**/ ?>