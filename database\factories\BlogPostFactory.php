<?php

namespace Database\Factories;

use App\Models\BlogPost;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BlogPost>
 */
class BlogPostFactory extends Factory
{
    protected $model = BlogPost::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(6, true);
        $publishedAt = $this->faker->dateTimeBetween('-6 months', 'now');
        
        // Logistics-focused tags
        $logisticsTags = [
            'logistics', 'supply chain', 'shipping', 'freight', 'transportation',
            'warehouse', 'distribution', 'inventory management', 'last mile delivery',
            'international shipping', 'customs', 'trade', 'e-commerce logistics',
            'sustainability', 'green logistics', 'technology', 'automation',
            'AI in logistics', 'digital transformation', 'cost optimization'
        ];
        
        $selectedTags = $this->faker->randomElements($logisticsTags, $this->faker->numberBetween(3, 6));
        
        $content = $this->generateLogisticsContent();
        
        return [
            'title' => rtrim($title, '.'),
            'slug' => Str::slug($title),
            'excerpt' => $this->faker->paragraph(2),
            'content' => $content,
            'featured_image' => null, // Will be set by specific seeders
            'author_id' => User::where('role', 'admin')->inRandomOrder()->first()?->id ?? 1,
            'status' => 'published',
            'published_at' => $publishedAt,
            'meta_title' => rtrim($title, '.') . ' - Atrix Logistics',
            'meta_description' => $this->faker->sentence(15),
            'meta_keywords' => implode(', ', $selectedTags),
            'tags' => $selectedTags,
            'reading_time' => $this->faker->numberBetween(3, 12),
            'view_count' => $this->faker->numberBetween(50, 2500),
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'sort_order' => $this->faker->numberBetween(1, 100),
        ];
    }

    /**
     * Generate logistics-focused content
     */
    private function generateLogisticsContent(): string
    {
        $paragraphs = [
            "The logistics industry continues to evolve rapidly, driven by technological advancements and changing consumer expectations. Companies are increasingly adopting digital solutions to streamline their operations and improve efficiency.",
            
            "Supply chain management has become more complex with global trade dynamics and the need for greater transparency. Organizations are investing in real-time tracking systems and predictive analytics to better manage their inventory and reduce costs.",
            
            "Last-mile delivery remains one of the most challenging aspects of logistics, particularly in urban areas. Innovative solutions such as drone delivery, autonomous vehicles, and micro-fulfillment centers are being explored to address these challenges.",
            
            "Sustainability has emerged as a critical factor in logistics decision-making. Companies are implementing green logistics practices, optimizing routes to reduce carbon emissions, and exploring alternative fuel options for their fleet operations.",
            
            "The integration of artificial intelligence and machine learning in logistics operations is transforming how companies forecast demand, optimize routes, and manage warehouse operations. These technologies enable more accurate predictions and automated decision-making.",
            
            "International shipping faces numerous challenges including regulatory compliance, customs procedures, and documentation requirements. Digital platforms are simplifying these processes and reducing the time and cost associated with cross-border trade.",
            
            "Warehouse automation is revolutionizing storage and fulfillment operations. Robotic systems, automated sorting, and smart inventory management are increasing efficiency while reducing labor costs and human error.",
            
            "The rise of e-commerce has fundamentally changed logistics requirements, with consumers expecting faster delivery times and greater flexibility. This has led to the development of new fulfillment strategies and distribution networks."
        ];
        
        $selectedParagraphs = $this->faker->randomElements($paragraphs, $this->faker->numberBetween(4, 6));
        
        return implode("\n\n", $selectedParagraphs);
    }

    /**
     * Indicate that the blog post is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the blog post is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'published_at' => null,
        ]);
    }

    /**
     * Indicate that the blog post is archived.
     */
    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'archived',
        ]);
    }
}
