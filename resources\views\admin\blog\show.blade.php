@extends('layouts.admin')

@section('title', 'Blog Post: ' . $blogPost->title)
@section('page-title', 'Blog Post Details')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.blog.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Posts
        </a>
        <a href="{{ route('admin.blog.edit', $blogPost) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Post
        </a>
        <a href="{{ route('blog.show', $blogPost) }}" class="btn btn-outline-info" target="_blank">
            <i class="fas fa-external-link-alt me-1"></i> View Live
        </a>
        <button type="button" class="btn btn-outline-danger" onclick="deletePost()">
            <i class="fas fa-trash me-1"></i> Delete
        </button>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold text-primary">{{ $blogPost->title }}</h5>
                </div>
                <div class="card-body">
                    <!-- Featured Image -->
                    @if($blogPost->featured_image)
                        <div class="mb-4 text-center">
                            <h6 class="text-muted mb-2">Featured Image</h6>
                            <img src="{{ Storage::url($blogPost->featured_image) }}"
                                 alt="{{ $blogPost->title }}"
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 400px;">
                        </div>
                    @endif

                    <!-- Additional Image -->
                    @if($blogPost->additional_image)
                        <div class="mb-4 text-center">
                            <h6 class="text-muted mb-2">Additional Image</h6>
                            <img src="{{ Storage::url($blogPost->additional_image) }}"
                                 alt="{{ $blogPost->title }}"
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 400px;">
                        </div>
                    @endif

                    <!-- Excerpt -->
                    @if($blogPost->excerpt)
                        <div class="alert alert-light border-start border-primary border-4 mb-4">
                            <h6 class="text-primary mb-2">Excerpt</h6>
                            <p class="mb-0">{{ $blogPost->excerpt }}</p>
                        </div>
                    @endif

                    <!-- Content -->
                    <div class="blog-content">
                        {!! nl2br(e($blogPost->content)) !!}
                    </div>

                    <!-- Tags -->
                    @if($blogPost->tags && count($blogPost->tags) > 0)
                        <div class="mt-4">
                            <h6 class="text-muted mb-2">Tags</h6>
                            @foreach($blogPost->tags as $tag)
                                <span class="badge bg-secondary me-1">{{ $tag }}</span>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>

            <!-- SEO Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-search me-2"></i>
                        SEO Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Meta Title</h6>
                            <p>{{ $blogPost->meta_title ?: $blogPost->title }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Meta Keywords</h6>
                            <p>{{ $blogPost->meta_keywords ?: 'Not set' }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted">Meta Description</h6>
                            <p>{{ $blogPost->meta_description ?: $blogPost->excerpt ?: 'Not set' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Post Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Post Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Status</h6>
                            @if($blogPost->status === 'published')
                                <span class="badge bg-success">Published</span>
                            @elseif($blogPost->status === 'draft')
                                <span class="badge bg-warning">Draft</span>
                            @else
                                <span class="badge bg-secondary">Archived</span>
                            @endif
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Featured</h6>
                            @if($blogPost->is_featured)
                                <span class="badge bg-warning">
                                    <i class="fas fa-star me-1"></i>Featured
                                </span>
                            @else
                                <span class="text-muted">No</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Author</h6>
                            <p class="mb-0">{{ $blogPost->author->name ?? 'Unknown' }}</p>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Reading Time</h6>
                            <p class="mb-0">{{ $blogPost->reading_time ?? 5 }} min</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Views</h6>
                            <p class="mb-0">{{ number_format($blogPost->view_count) }}</p>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Sort Order</h6>
                            <p class="mb-0">{{ $blogPost->sort_order ?? 0 }}</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <h6 class="text-muted mb-1">Slug</h6>
                            <p class="mb-0 text-break">{{ $blogPost->slug }}</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Created</h6>
                            <p class="mb-0">{{ $blogPost->created_at->format('M d, Y') }}</p>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Updated</h6>
                            <p class="mb-0">{{ $blogPost->updated_at->format('M d, Y') }}</p>
                        </div>
                    </div>

                    @if($blogPost->published_at)
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted mb-1">Published</h6>
                                <p class="mb-0">{{ $blogPost->published_at->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($blogPost->status === 'draft')
                            <form method="POST" action="{{ route('admin.blog.update', $blogPost) }}" style="display: inline;">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="status" value="published">
                                <input type="hidden" name="title" value="{{ $blogPost->title }}">
                                <input type="hidden" name="content" value="{{ $blogPost->content }}">
                                <input type="hidden" name="author_id" value="{{ $blogPost->author_id }}">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-globe me-1"></i> Publish Now
                                </button>
                            </form>
                        @elseif($blogPost->status === 'published')
                            <form method="POST" action="{{ route('admin.blog.update', $blogPost) }}" style="display: inline;">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="status" value="draft">
                                <input type="hidden" name="title" value="{{ $blogPost->title }}">
                                <input type="hidden" name="content" value="{{ $blogPost->content }}">
                                <input type="hidden" name="author_id" value="{{ $blogPost->author_id }}">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-eye-slash me-1"></i> Unpublish
                                </button>
                            </form>
                        @endif

                        <button type="button" class="btn btn-outline-primary" onclick="duplicatePost()">
                            <i class="fas fa-copy me-1"></i> Duplicate Post
                        </button>

                        <a href="{{ route('admin.blog.create') }}" class="btn btn-outline-success">
                            <i class="fas fa-plus me-1"></i> Create New Post
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Blog Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this blog post? This action cannot be undone.</p>
                <p><strong>{{ $blogPost->title }}</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ route('admin.blog.destroy', $blogPost) }}" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.blog-content {
    line-height: 1.8;
    font-size: 1.1rem;
}

.blog-content p {
    margin-bottom: 1rem;
}
</style>
@endpush

@push('scripts')
<script>
function deletePost() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function duplicatePost() {
    // Implementation for duplicating post
    alert('Duplicate functionality coming soon!');
}
</script>
@endpush
