@extends('layouts.admin')

@section('title', 'Add Slider')
@section('page-title', 'Add New Slider')

@section('page-actions')
    <a href="{{ route('admin.cms.sliders.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Sliders
    </a>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ route('admin.cms.sliders.store') }}" enctype="multipart/form-data">
                @csrf
                
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Slider Content</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                                   id="subtitle" name="subtitle" value="{{ old('subtitle') }}">
                            @error('subtitle')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" 
                                      placeholder="Brief description for the slider...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Maximum 1000 characters</small>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Call-to-Action Button</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="button_text" class="form-label">Button Text</label>
                                <input type="text" class="form-control @error('button_text') is-invalid @enderror" 
                                       id="button_text" name="button_text" value="{{ old('button_text') }}"
                                       placeholder="e.g., Get Started, Learn More">
                                @error('button_text')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="button_url" class="form-label">Button URL</label>
                                <input type="url" class="form-control @error('button_url') is-invalid @enderror" 
                                       id="button_url" name="button_url" value="{{ old('button_url') }}"
                                       placeholder="https://example.com or /page">
                                @error('button_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <small class="text-muted">Leave both fields empty if you don't want a button</small>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Display Settings</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="display_order" class="form-label">Display Order</label>
                                <input type="number" class="form-control @error('display_order') is-invalid @enderror" 
                                       id="display_order" name="display_order" value="{{ old('display_order') }}" 
                                       min="0" step="1">
                                @error('display_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Leave empty to add at the end</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Active Status</strong>
                                        <br><small class="text-muted">Show this slider on the website</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Upload Section -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Desktop Image <span class="text-danger">*</span></h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div id="desktop-preview" class="mb-3 text-center">
                                <div class="bg-light d-flex align-items-center justify-content-center mx-auto"
                                     style="width: 100%; height: 200px; border: 2px dashed #ddd;" id="default-desktop">
                                    <div class="text-center">
                                        <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                        <div class="text-muted">Desktop Image Preview</div>
                                    </div>
                                </div>
                                <img id="desktop-preview-image" src="#" alt="Desktop Preview"
                                     class="img-fluid mx-auto"
                                     style="width: 100%; height: 200px; object-fit: cover; display: none;">
                            </div>

                            <input type="file" class="form-control @error('image') is-invalid @enderror"
                                   id="image" name="image" accept="image/*" onchange="previewImage(this, 'desktop')" required>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted d-block mt-2">
                                Supported formats: JPG, PNG, GIF, WebP<br>
                                Maximum size: 5MB<br>
                                Recommended: 1920x800px
                            </small>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Mobile Image (Optional)</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div id="mobile-preview" class="mb-3 text-center">
                                <div class="bg-light d-flex align-items-center justify-content-center mx-auto"
                                     style="width: 200px; height: 300px; border: 2px dashed #ddd;" id="default-mobile">
                                    <div class="text-center">
                                        <i class="fas fa-mobile-alt fa-3x text-muted mb-2"></i>
                                        <div class="text-muted small">Mobile Image Preview</div>
                                    </div>
                                </div>
                                <img id="mobile-preview-image" src="#" alt="Mobile Preview"
                                     class="img-fluid mx-auto"
                                     style="width: 200px; height: 300px; object-fit: cover; display: none;">
                            </div>

                            <input type="file" class="form-control @error('mobile_image') is-invalid @enderror"
                                   id="mobile_image" name="mobile_image" accept="image/*" onchange="previewImage(this, 'mobile')">
                            @error('mobile_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted d-block mt-2">
                                Supported formats: JPG, PNG, GIF, WebP<br>
                                Maximum size: 5MB<br>
                                Recommended: 768x1024px
                            </small>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <a href="{{ route('admin.cms.sliders.index') }}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times me-1"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Create Slider
                    </button>
                </div>
            </form>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Tips & Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-3">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <strong>Image Quality:</strong><br>
                            <small class="text-muted">Use high-quality images for best visual impact. Desktop images should be at least 1920x800px.</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-mobile-alt text-info me-2"></i>
                            <strong>Mobile Optimization:</strong><br>
                            <small class="text-muted">Mobile images ensure optimal mobile experience. Recommended size: 768x1024px.</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-edit text-success me-2"></i>
                            <strong>Content:</strong><br>
                            <small class="text-muted">Keep text concise and action-oriented. Use compelling headlines and clear call-to-action buttons.</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-sort-numeric-up text-primary me-2"></i>
                            <strong>Display Order:</strong><br>
                            <small class="text-muted">Use display order to control slider sequence. Lower numbers appear first.</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-file-image text-secondary me-2"></i>
                            <strong>File Requirements:</strong><br>
                            <small class="text-muted">Supported formats: JPG, PNG, GIF, WebP. Maximum size: 5MB per image.</small>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Preview</h6>
                </div>
                <div class="card-body text-center">
                    <div class="bg-light rounded p-4">
                        <i class="fas fa-eye fa-3x text-muted mb-3"></i>
                        <p class="text-muted mb-0">
                            <small>Your slider will appear on the homepage after creation. Make sure to activate it in the display settings.</small>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function previewImage(input, type) {
        const preview = document.getElementById(`${type}-preview-image`);
        const defaultPreview = document.getElementById(`default-${type}`);

        if (input.files && input.files[0]) {
            const file = input.files[0];

            // Validate file size (5MB max)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB');
                input.value = '';
                preview.style.display = 'none';
                defaultPreview.style.display = 'flex';
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid image file (JPG, PNG, GIF, WebP)');
                input.value = '';
                preview.style.display = 'none';
                defaultPreview.style.display = 'flex';
                return;
            }

            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
                defaultPreview.style.display = 'none';
            };

            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
            defaultPreview.style.display = 'flex';
        }
    }

    // Form submission validation and debugging
    document.querySelector('form').addEventListener('submit', function(e) {
        const imageInput = document.getElementById('image');
        const submitBtn = document.querySelector('button[type="submit"]');

        console.log('=== FORM SUBMISSION DEBUG ===');
        console.log('Form element:', this);
        console.log('Form action:', this.action);
        console.log('Form method:', this.method);
        console.log('Form enctype:', this.enctype);

        // Check if required image is selected
        console.log('Image input element:', imageInput);
        console.log('Image input files:', imageInput.files);
        console.log('Image input files length:', imageInput.files ? imageInput.files.length : 'null');

        if (!imageInput.files || !imageInput.files[0]) {
            e.preventDefault();
            console.error('❌ No image file selected');
            alert('Please select a desktop image before creating the slider.');
            imageInput.focus();
            return false;
        }

        const imageFile = imageInput.files[0];
        console.log('✅ Image file selected:', {
            name: imageFile.name,
            size: imageFile.size,
            type: imageFile.type,
            lastModified: imageFile.lastModified
        });

        // Validate file size and type again
        if (imageFile.size > 5 * 1024 * 1024) {
            e.preventDefault();
            console.error('❌ File too large:', imageFile.size);
            alert('File size must be less than 5MB');
            return false;
        }

        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(imageFile.type)) {
            e.preventDefault();
            console.error('❌ Invalid file type:', imageFile.type);
            alert('Please select a valid image file (JPG, PNG, GIF, WebP)');
            return false;
        }

        // Show loading state
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Creating Slider...';
        }

        // Create and log FormData
        const formData = new FormData(this);
        console.log('📋 FormData created. Entries:');
        let hasImageInFormData = false;

        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(`${key}: File - ${value.name} (${value.size} bytes, ${value.type})`);
                if (key === 'image') {
                    hasImageInFormData = true;
                }
            } else {
                console.log(`${key}: ${value}`);
            }
        }

        if (!hasImageInFormData) {
            e.preventDefault();
            console.error('❌ Image not found in FormData!');
            alert('Error: Image file not properly attached to form. Please try again.');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-1"></i> Create Slider';
            }
            return false;
        }

        console.log('✅ Form validation passed, submitting...');
        console.log('=== END DEBUG ===');
    });

    // Character counter for description
    document.getElementById('description').addEventListener('input', function() {
        const maxLength = 1000;
        const currentLength = this.value.length;
        const remaining = maxLength - currentLength;

        // Find or create counter element
        let counter = document.getElementById('description-counter');
        if (!counter) {
            counter = document.createElement('small');
            counter.id = 'description-counter';
            counter.className = 'text-muted';
            this.parentNode.appendChild(counter);
        }

        counter.textContent = `${currentLength}/${maxLength} characters`;
        counter.className = remaining < 100 ? 'text-warning' : (remaining < 50 ? 'text-danger' : 'text-muted');
    });

    // Auto-fill button URL based on button text
    document.getElementById('button_text').addEventListener('input', function() {
        const buttonUrl = document.getElementById('button_url');
        if (!buttonUrl.value && this.value) {
            const text = this.value.toLowerCase();
            if (text.includes('quote') || text.includes('get started')) {
                buttonUrl.value = '/quote';
            } else if (text.includes('track')) {
                buttonUrl.value = '/track';
            } else if (text.includes('about') || text.includes('learn more')) {
                buttonUrl.value = '/about';
            } else if (text.includes('contact')) {
                buttonUrl.value = '/contact';
            } else if (text.includes('service')) {
                buttonUrl.value = '/services';
            }
        }
    });
</script>
@endpush
