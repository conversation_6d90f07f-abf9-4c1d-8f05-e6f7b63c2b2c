<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageOptimizationService
{
    protected $imageManager;
    protected $maxWidth = 1920;
    protected $maxHeight = 1080;
    protected $quality = 85;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Optimize and convert image to WebP format
     */
    public function optimizeAndConvert(UploadedFile $file, string $directory = 'uploads', array $options = []): string
    {
        // Extract options
        $maxWidth = $options['max_width'] ?? $this->maxWidth;
        $maxHeight = $options['max_height'] ?? $this->maxHeight;
        $quality = $options['quality'] ?? $this->quality;
        $generateThumbnail = $options['generate_thumbnail'] ?? false;

        // Generate unique filename
        $filename = $this->generateFilename($file->getClientOriginalName());
        $webpFilename = $filename . '.webp';
        $path = $directory . '/' . $webpFilename;

        // Read and process the image
        $image = $this->imageManager->read($file->getPathname());

        // Resize if needed while maintaining aspect ratio
        if ($image->width() > $maxWidth || $image->height() > $maxHeight) {
            $image->scale(width: $maxWidth, height: $maxHeight);
        }

        // Convert to WebP and save
        $webpData = $image->toWebp($quality);
        Storage::disk('public')->put($path, $webpData);

        // Generate thumbnail if requested
        if ($generateThumbnail) {
            $this->generateThumbnail($image, $directory, $filename, $options);
        }

        return $path;
    }

    /**
     * Generate thumbnail version
     */
    protected function generateThumbnail($image, string $directory, string $filename, array $options = []): string
    {
        $thumbWidth = $options['thumb_width'] ?? 400;
        $thumbHeight = $options['thumb_height'] ?? 300;
        $thumbQuality = $options['thumb_quality'] ?? 80;

        $thumbPath = $directory . '/thumbs/' . $filename . '_thumb.webp';
        
        // Create thumbnail
        $thumbnail = clone $image;
        $thumbnail->scale(width: $thumbWidth, height: $thumbHeight);
        
        $thumbData = $thumbnail->toWebp($thumbQuality);
        Storage::disk('public')->put($thumbPath, $thumbData);

        return $thumbPath;
    }

    /**
     * Optimize slider images with specific dimensions
     */
    public function optimizeSliderImage(UploadedFile $file, string $type = 'desktop'): string
    {
        $options = $this->getSliderOptions($type);
        return $this->optimizeAndConvert($file, 'uploads/sliders', $options);
    }

    /**
     * Get optimization options for different slider types
     */
    protected function getSliderOptions(string $type): array
    {
        switch ($type) {
            case 'mobile':
                return [
                    'max_width' => 768,
                    'max_height' => 1024,
                    'quality' => 85,
                    'generate_thumbnail' => true,
                    'thumb_width' => 200,
                    'thumb_height' => 300,
                ];
            case 'desktop':
            default:
                return [
                    'max_width' => 1920,
                    'max_height' => 800,
                    'quality' => 85,
                    'generate_thumbnail' => true,
                    'thumb_width' => 400,
                    'thumb_height' => 200,
                ];
        }
    }

    /**
     * Generate unique filename
     */
    protected function generateFilename(string $originalName): string
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $name = pathinfo($originalName, PATHINFO_FILENAME);
        $name = preg_replace('/[^a-zA-Z0-9_-]/', '', $name);
        $name = substr($name, 0, 50); // Limit length
        
        return $name . '_' . time() . '_' . uniqid();
    }

    /**
     * Delete optimized images
     */
    public function deleteOptimizedImages(string $imagePath): bool
    {
        $deleted = true;

        // Delete main image
        if (Storage::disk('public')->exists($imagePath)) {
            $deleted = Storage::disk('public')->delete($imagePath) && $deleted;
        }

        // Delete thumbnail if exists
        $pathInfo = pathinfo($imagePath);
        $thumbPath = $pathInfo['dirname'] . '/thumbs/' . $pathInfo['filename'] . '_thumb.webp';
        
        if (Storage::disk('public')->exists($thumbPath)) {
            $deleted = Storage::disk('public')->delete($thumbPath) && $deleted;
        }

        return $deleted;
    }

    /**
     * Get image dimensions
     */
    public function getImageDimensions(string $imagePath): array
    {
        if (!Storage::disk('public')->exists($imagePath)) {
            return ['width' => 0, 'height' => 0];
        }

        $fullPath = Storage::disk('public')->path($imagePath);
        $image = $this->imageManager->read($fullPath);

        return [
            'width' => $image->width(),
            'height' => $image->height(),
        ];
    }

    /**
     * Get file size in human readable format
     */
    public function getFileSize(string $imagePath): string
    {
        if (!Storage::disk('public')->exists($imagePath)) {
            return '0 B';
        }

        $bytes = Storage::disk('public')->size($imagePath);
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if WebP is supported
     */
    public function isWebPSupported(): bool
    {
        return function_exists('imagewebp');
    }

    /**
     * Get image info for admin display
     */
    public function getImageInfo(string $imagePath): array
    {
        if (!Storage::disk('public')->exists($imagePath)) {
            return [
                'exists' => false,
                'url' => null,
                'size' => '0 B',
                'dimensions' => ['width' => 0, 'height' => 0],
            ];
        }

        $dimensions = $this->getImageDimensions($imagePath);
        
        return [
            'exists' => true,
            'url' => Storage::url($imagePath),
            'size' => $this->getFileSize($imagePath),
            'dimensions' => $dimensions,
            'aspect_ratio' => $dimensions['height'] > 0 ? round($dimensions['width'] / $dimensions['height'], 2) : 0,
        ];
    }
}
