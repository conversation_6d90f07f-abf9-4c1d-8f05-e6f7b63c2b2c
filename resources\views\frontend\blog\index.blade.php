@extends('layouts.frontend')

@section('title', 'Blog - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics'))
@section('description', 'Stay updated with the latest logistics industry insights, tips, and news from our expert team.')

@section('content')

<!-- Hero Section -->
<x-page-hero 
    title="Logistics <span class='text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400'>Insights</span>"
    subtitle="Stay updated with industry trends and expert insights"
    description="Discover the latest in logistics, shipping, and supply chain management from our team of experts."
    :breadcrumbs="[
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Blog']
    ]"
    gradient="from-purple-900 via-blue-800 to-green-900"
/>

<!-- Blog Posts Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl lg:text-5xl font-bold font-heading text-gray-900 mb-6">
                Latest <span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600">Articles</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Expert insights, industry trends, and practical tips to help you navigate the world of logistics and shipping.
            </p>
        </div>

        @if($posts->count() > 0)
        <!-- Blog Posts Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            @foreach($posts as $post)
            <article class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden animate-on-scroll">
                <!-- Featured Image -->
                <div class="relative h-64 bg-gradient-to-br from-purple-500 to-blue-500 overflow-hidden">
                    @if($post->featured_image)
                        <img src="{{ Storage::url($post->featured_image) }}" alt="{{ $post->title }}" 
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                    @else
                        <div class="w-full h-full flex items-center justify-center">
                            <div class="text-center text-white">
                                <i class="fas fa-newspaper text-4xl mb-4 opacity-80"></i>
                                <p class="text-lg font-semibold opacity-90">{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</p>
                            </div>
                        </div>
                    @endif
                    
                    <!-- Date Badge -->
                    <div class="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                        <span class="text-sm font-semibold text-gray-800">
                            {{ $post->published_at->format('M d, Y') }}
                        </span>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-8">
                    <div class="mb-4">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors line-clamp-2">
                            {{ $post->title }}
                        </h3>
                        <p class="text-gray-600 leading-relaxed line-clamp-3">
                            {{ $post->excerpt }}
                        </p>
                    </div>

                    <!-- Meta Info -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-user-circle mr-2 text-purple-500"></i>
                            {{ $post->author->name ?? 'Admin' }}
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2 text-blue-500"></i>
                            {{ $post->published_at->diffForHumans() }}
                        </div>
                    </div>

                    <!-- Read More Button -->
                    <a href="{{ route('blog.show', $post->slug) }}" 
                       class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg font-semibold transition-all duration-300 group-hover:shadow-lg">
                        <span>Read More</span>
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </article>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-16 animate-on-scroll">
            {{ $posts->links('pagination::tailwind') }}
        </div>
        @else
        <!-- No Posts State -->
        <div class="text-center py-20 animate-on-scroll">
            <div class="max-w-md mx-auto">
                <i class="fas fa-newspaper text-6xl text-gray-400 mb-6"></i>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Coming Soon</h3>
                <p class="text-gray-600 mb-6">We're working on bringing you the latest logistics insights and industry news. Check back soon!</p>
                <a href="{{ route('contact') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    Contact Us for Updates
                </a>
            </div>
        </div>
        @endif
    </div>
</section>

<!-- Newsletter Subscription -->
<section class="py-20 bg-gradient-to-br from-purple-900 via-blue-800 to-green-900 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-4xl mx-auto text-center animate-on-scroll">
            <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
                Stay <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400">Informed</span>
            </h2>
            <p class="text-xl text-gray-300 mb-8">
                Subscribe to our newsletter and never miss the latest logistics insights, industry trends, and expert tips.
            </p>

            <!-- Newsletter Form -->
            <form action="{{ route('newsletter.subscribe') }}" method="POST" class="max-w-md mx-auto" id="blog-newsletter-form">
                @csrf
                <div class="flex gap-4">
                    <input type="email" name="email" required
                           class="flex-1 px-6 py-4 rounded-xl border-0 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300"
                           placeholder="Enter your email address">
                    <button type="submit"
                            class="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-paper-plane mr-2"></i>
                        <span class="button-text">Subscribe</span>
                    </button>
                </div>
                <div id="blog-newsletter-message" class="mt-4 text-center hidden"></div>
            </form>
        </div>
    </div>
</section>

@endsection

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('blog-newsletter-form');
    const messageDiv = document.getElementById('blog-newsletter-message');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const buttonText = submitBtn.querySelector('.button-text');
            const originalText = buttonText.textContent;

            // Show loading state
            submitBtn.disabled = true;
            buttonText.textContent = 'Subscribing...';
            messageDiv.classList.add('hidden');

            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageDiv.className = 'mt-4 text-center text-green-300 bg-green-900/20 px-4 py-2 rounded-lg';
                    messageDiv.textContent = data.message;
                    form.reset();
                } else {
                    throw new Error(data.message || 'Subscription failed');
                }
            })
            .catch(error => {
                messageDiv.className = 'mt-4 text-center text-red-300 bg-red-900/20 px-4 py-2 rounded-lg';
                messageDiv.textContent = error.message || 'An error occurred. Please try again.';
            })
            .finally(() => {
                submitBtn.disabled = false;
                buttonText.textContent = originalText;
                messageDiv.classList.remove('hidden');

                // Hide message after 5 seconds
                setTimeout(() => {
                    messageDiv.classList.add('hidden');
                }, 5000);
            });
        });
    }
});
</script>
@endpush
