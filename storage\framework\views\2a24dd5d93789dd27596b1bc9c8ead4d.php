<?php $__env->startSection('title', 'Our Services - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics')); ?>
<?php $__env->startSection('description', 'Comprehensive logistics and shipping services including express delivery, international shipping, freight services, and more.'); ?>

<?php $__env->startSection('content'); ?>

<!-- Hero Section -->
<?php if (isset($component)) { $__componentOriginala9d931d4f11b4d2850df99e991db1dca = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9d931d4f11b4d2850df99e991db1dca = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-hero','data' => ['title' => 'Our <span class=\'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400\'>Services</span>','subtitle' => 'Comprehensive Logistics Solutions for Every Need','description' => 'From express delivery to global freight, we provide end-to-end logistics services that keep your business moving forward.','breadcrumbs' => [
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Services']
    ],'gradient' => 'from-blue-900 via-gray-800 to-green-900']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Our <span class=\'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400\'>Services</span>','subtitle' => 'Comprehensive Logistics Solutions for Every Need','description' => 'From express delivery to global freight, we provide end-to-end logistics services that keep your business moving forward.','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Services']
    ]),'gradient' => 'from-blue-900 via-gray-800 to-green-900']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $attributes = $__attributesOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $component = $__componentOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__componentOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>

<!-- Services Section -->
<section class="py-20 bg-gray-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23000000" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Section Header -->
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl lg:text-5xl font-bold font-heading text-gray-900 mb-6">
                Comprehensive <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">Logistics Solutions</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We provide end-to-end logistics services to meet all your shipping and transportation needs with precision, reliability, and innovation.
            </p>
        </div>

        <!-- Services Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            <!-- Truck Freight Service -->
            <div class="group animate-on-scroll">
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative">
                    <!-- Image -->
                    <div class="relative h-64 overflow-hidden">
                        <img src="<?php echo e(asset('assets/images/service/service-13.jpg')); ?>" alt="Truck Freight Service"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <!-- Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <!-- Icon -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-truck text-2xl text-blue-600"></i>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                            Truck Freight Service
                        </h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Reliable ground transportation for all your freight needs across the country. Fast, secure, and cost-effective solutions.
                        </p>

                        <!-- Features -->
                        <ul class="space-y-3 mb-6">
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                Nationwide coverage
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                Real-time tracking
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                Flexible scheduling
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                Competitive pricing
                            </li>
                        </ul>

                        <!-- CTA Button -->
                        <button onclick="openQuoteModal()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-xl font-semibold transition-colors group-hover:shadow-lg">
                            Get Quote
                        </button>
                    </div>
                </div>
            </div>
            <!-- Ship Freight Service -->
            <div class="group animate-on-scroll">
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative">
                    <!-- Image -->
                    <div class="relative h-64 overflow-hidden">
                        <img src="<?php echo e(asset('assets/images/service/service-14.jpg')); ?>" alt="Ship Freight Service"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <!-- Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <!-- Icon -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-ship text-2xl text-green-600"></i>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors">
                            Ship Freight Service
                        </h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            International shipping solutions for global trade and commerce. Reliable ocean freight services worldwide.
                        </p>

                        <!-- Features -->
                        <ul class="space-y-3 mb-6">
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                Global shipping network
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                Container services
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                Customs clearance
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                Door-to-door delivery
                            </li>
                        </ul>

                        <!-- CTA Button -->
                        <button onclick="openQuoteModal()" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-xl font-semibold transition-colors group-hover:shadow-lg">
                            Get Quote
                        </button>
                    </div>
                </div>
            </div>
            <!-- Air Freight Service -->
            <div class="group animate-on-scroll">
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative">
                    <!-- Image -->
                    <div class="relative h-64 overflow-hidden">
                        <img src="<?php echo e(asset('assets/images/service/service-15.jpg')); ?>" alt="Air Freight Service"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <!-- Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <!-- Icon -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-plane text-2xl text-purple-600"></i>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors">
                            Air Freight Service
                        </h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Fast and secure air transportation for urgent deliveries worldwide. Express shipping when time matters most.
                        </p>

                        <!-- Features -->
                        <ul class="space-y-3 mb-6">
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                                Express delivery
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                                Temperature controlled
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                                Priority handling
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                                Same-day delivery
                            </li>
                        </ul>

                        <!-- CTA Button -->
                        <button onclick="openQuoteModal()" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-xl font-semibold transition-colors group-hover:shadow-lg">
                            Get Quote
                        </button>
                    </div>
                </div>
            </div>
            <!-- Warehousing Solutions -->
            <div class="group animate-on-scroll">
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative">
                    <!-- Image -->
                    <div class="relative h-64 overflow-hidden">
                        <img src="<?php echo e(asset('assets/images/service/service-13.jpg')); ?>" alt="Warehousing Solutions"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <!-- Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <!-- Icon -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-warehouse text-2xl text-orange-600"></i>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-orange-600 transition-colors">
                            Warehousing Solutions
                        </h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Secure storage facilities with advanced inventory management systems. Perfect for businesses of all sizes.
                        </p>

                        <!-- Features -->
                        <ul class="space-y-3 mb-6">
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                                Climate controlled
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                                24/7 security
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                                Inventory management
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                                Pick & pack services
                            </li>
                        </ul>

                        <!-- CTA Button -->
                        <button onclick="openQuoteModal()" class="w-full bg-orange-600 hover:bg-orange-700 text-white py-3 px-6 rounded-xl font-semibold transition-colors group-hover:shadow-lg">
                            Get Quote
                        </button>
                    </div>
                </div>
            </div>
            <!-- Supply Chain Management -->
            <div class="group animate-on-scroll">
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative">
                    <!-- Image -->
                    <div class="relative h-64 overflow-hidden">
                        <img src="<?php echo e(asset('assets/images/service/service-14.jpg')); ?>" alt="Supply Chain Management"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <!-- Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <!-- Icon -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-network-wired text-2xl text-indigo-600"></i>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-indigo-600 transition-colors">
                            Supply Chain Management
                        </h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            End-to-end supply chain optimization to streamline your operations and reduce costs.
                        </p>

                        <!-- Features -->
                        <ul class="space-y-3 mb-6">
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-indigo-500 rounded-full mr-3"></div>
                                Process optimization
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-indigo-500 rounded-full mr-3"></div>
                                Cost reduction
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-indigo-500 rounded-full mr-3"></div>
                                Risk management
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-indigo-500 rounded-full mr-3"></div>
                                Performance analytics
                            </li>
                        </ul>

                        <!-- CTA Button -->
                        <button onclick="openQuoteModal()" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-6 rounded-xl font-semibold transition-colors group-hover:shadow-lg">
                            Get Quote
                        </button>
                    </div>
                </div>
            </div>

            <!-- Express Delivery -->
            <div class="group animate-on-scroll">
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative">
                    <!-- Image -->
                    <div class="relative h-64 overflow-hidden">
                        <img src="<?php echo e(asset('assets/images/service/service-15.jpg')); ?>" alt="Express Delivery"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        <!-- Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                        <!-- Icon -->
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-bolt text-2xl text-red-600"></i>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors">
                            Express Delivery
                        </h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Fast and reliable express delivery services for urgent shipments. Same-day and next-day options available.
                        </p>

                        <!-- Features -->
                        <ul class="space-y-3 mb-6">
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                                Same-day delivery
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                                Next-day delivery
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                                Time-definite service
                            </li>
                            <li class="flex items-center text-gray-700">
                                <div class="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                                Signature confirmation
                            </li>
                        </ul>

                        <!-- CTA Button -->
                        <button onclick="openQuoteModal()" class="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-6 rounded-xl font-semibold transition-colors group-hover:shadow-lg">
                            Get Quote
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="text-center mt-16 animate-on-scroll">
            <button onclick="openQuoteModal()" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
                <i class="fas fa-calculator mr-3"></i>
                Get A Comprehensive Quote
            </button>
        </div>
    </div>
</section>

<!-- Why Choose Our Services Section -->
<section class="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-blue-900 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0">
        <!-- Animated Background -->
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>

        <!-- Floating Elements -->
        <div class="absolute w-2 h-2 bg-blue-400 rounded-full animate-float" style="top: 15%; left: 15%; animation-delay: 0s;"></div>
        <div class="absolute w-1 h-1 bg-green-400 rounded-full animate-float" style="top: 70%; left: 85%; animation-delay: 2s;"></div>
        <div class="absolute w-3 h-3 bg-white rounded-full animate-float" style="top: 45%; left: 10%; animation-delay: 1s;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Section Header -->
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl lg:text-5xl font-bold font-heading text-white mb-6">
                Why Choose <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400">Our Services?</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                We provide comprehensive logistics solutions with a focus on reliability, speed, and customer satisfaction that drives your business forward.
            </p>
        </div>

        <!-- Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            <!-- Professional Team -->
            <div class="group text-center animate-on-scroll">
                <div class="relative mb-6">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-2xl text-white"></i>
                    </div>
                    <!-- Glow Effect -->
                    <div class="absolute inset-0 w-20 h-20 bg-blue-500/30 rounded-2xl blur-xl mx-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <h3 class="text-xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors">Professional Team</h3>
                <p class="text-gray-300 leading-relaxed">Experienced logistics professionals dedicated to your success and growth.</p>
            </div>

            <!-- Advanced Technology -->
            <div class="group text-center animate-on-scroll">
                <div class="relative mb-6">
                    <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-microchip text-2xl text-white"></i>
                    </div>
                    <!-- Glow Effect -->
                    <div class="absolute inset-0 w-20 h-20 bg-green-500/30 rounded-2xl blur-xl mx-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <h3 class="text-xl font-bold text-white mb-3 group-hover:text-green-400 transition-colors">Advanced Technology</h3>
                <p class="text-gray-300 leading-relaxed">State-of-the-art tracking and management systems for complete visibility.</p>
            </div>

            <!-- Global Network -->
            <div class="group text-center animate-on-scroll">
                <div class="relative mb-6">
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-globe text-2xl text-white"></i>
                    </div>
                    <!-- Glow Effect -->
                    <div class="absolute inset-0 w-20 h-20 bg-purple-500/30 rounded-2xl blur-xl mx-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-400 transition-colors">Global Network</h3>
                <p class="text-gray-300 leading-relaxed">Worldwide coverage with local expertise and international reach.</p>
            </div>

            <!-- Flexible Solutions -->
            <div class="group text-center animate-on-scroll">
                <div class="relative mb-6">
                    <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-cogs text-2xl text-white"></i>
                    </div>
                    <!-- Glow Effect -->
                    <div class="absolute inset-0 w-20 h-20 bg-orange-500/30 rounded-2xl blur-xl mx-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <h3 class="text-xl font-bold text-white mb-3 group-hover:text-orange-400 transition-colors">Flexible Solutions</h3>
                <p class="text-gray-300 leading-relaxed">Customized services tailored to meet your specific business needs.</p>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 max-w-4xl mx-auto">
            <div class="text-center animate-on-scroll">
                <div class="text-3xl lg:text-4xl font-bold text-green-400 mb-2">99.9%</div>
                <div class="text-gray-300 font-medium">On-Time Delivery</div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="text-3xl lg:text-4xl font-bold text-blue-400 mb-2">24/7</div>
                <div class="text-gray-300 font-medium">Customer Support</div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="text-3xl lg:text-4xl font-bold text-purple-400 mb-2">50+</div>
                <div class="text-gray-300 font-medium">Countries Served</div>
            </div>
            <div class="text-center animate-on-scroll">
                <div class="text-3xl lg:text-4xl font-bold text-orange-400 mb-2">15+</div>
                <div class="text-gray-300 font-medium">Years Experience</div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Service card hover effects */
.group:hover .object-cover {
    transform: scale(1.1);
}

/* Icon animations */
.group:hover i {
    transform: scale(1.1) rotate(5deg);
}

/* Glow effects for feature icons */
.group:hover .blur-xl {
    opacity: 1;
}

/* Stagger animation delays */
.animate-on-scroll:nth-child(1) { transition-delay: 0.1s; }
.animate-on-scroll:nth-child(2) { transition-delay: 0.2s; }
.animate-on-scroll:nth-child(3) { transition-delay: 0.3s; }
.animate-on-scroll:nth-child(4) { transition-delay: 0.4s; }
.animate-on-scroll:nth-child(5) { transition-delay: 0.5s; }
.animate-on-scroll:nth-child(6) { transition-delay: 0.6s; }

/* Button pulse effect */
@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
}

.group:hover button {
    animation: pulse-glow 2s infinite;
}

/* Floating animation for background elements */
@keyframes float-slow {
    0%, 100% { transform: translateY(0px) translateX(0px); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(0px) translateX(10px); }
    75% { transform: translateY(10px) translateX(5px); }
}

.animate-float {
    animation: float-slow 8s ease-in-out infinite;
}

/* Service card entrance animation */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.animate-on-scroll.visible {
    animation: slideInUp 0.8s ease-out forwards;
}

/* Gradient text shimmer */
@keyframes shimmer {
    0% { background-position: -200% center; }
    100% { background-position: 200% center; }
}

.bg-clip-text {
    background-size: 200% auto;
    animation: shimmer 3s linear infinite;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Enhanced scroll animations with intersection observer
function initServiceAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                // Add staggered delay for grid items
                setTimeout(() => {
                    entry.target.classList.add('visible');
                }, index * 100);
            }
        });
    }, observerOptions);

    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// Service card interactions
function initServiceCardEffects() {
    const serviceCards = document.querySelectorAll('.group');

    serviceCards.forEach(card => {
        // Add hover sound effect (optional)
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });

        // Add click ripple effect
        card.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 10;
            `;

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => ripple.remove(), 600);
        });
    });
}

// Parallax effect for background elements
function initParallaxEffect() {
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.animate-float');

        parallaxElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    });
}

// Stats counter animation
function animateStats() {
    const stats = document.querySelectorAll('.text-3xl');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const text = target.textContent;
                const number = parseFloat(text.replace(/[^\d.]/g, ''));
                const suffix = text.replace(/[\d.]/g, '');

                if (!isNaN(number)) {
                    animateNumber(target, 0, number, suffix, 2000);
                }

                observer.unobserve(target);
            }
        });
    });

    stats.forEach(stat => observer.observe(stat));
}

function animateNumber(element, start, end, suffix, duration) {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const current = start + (end - start) * easeOut;

        element.textContent = current.toFixed(1) + suffix;

        if (progress < 1) {
            requestAnimationFrame(update);
        } else {
            element.textContent = end + suffix;
        }
    }

    requestAnimationFrame(update);
}

// Initialize all animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    initServiceAnimations();
    initServiceCardEffects();
    initParallaxEffect();
    animateStats();

    // Add CSS for ripple animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/frontend/services.blade.php ENDPATH**/ ?>