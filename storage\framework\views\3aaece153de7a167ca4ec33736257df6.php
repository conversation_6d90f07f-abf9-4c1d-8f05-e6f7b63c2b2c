<?php $__env->startSection('title', 'Blog - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics')); ?>
<?php $__env->startSection('description', 'Stay updated with the latest logistics industry insights, tips, and news from our expert team.'); ?>

<?php $__env->startSection('content'); ?>

<!-- Hero Section -->
<?php if (isset($component)) { $__componentOriginala9d931d4f11b4d2850df99e991db1dca = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9d931d4f11b4d2850df99e991db1dca = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-hero','data' => ['title' => 'Logistics <span class=\'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400\'>Insights</span>','subtitle' => 'Stay updated with industry trends and expert insights','description' => 'Discover the latest in logistics, shipping, and supply chain management from our team of experts.','breadcrumbs' => [
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Blog']
    ],'gradient' => 'from-purple-900 via-blue-800 to-green-900']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Logistics <span class=\'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400\'>Insights</span>','subtitle' => 'Stay updated with industry trends and expert insights','description' => 'Discover the latest in logistics, shipping, and supply chain management from our team of experts.','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Blog']
    ]),'gradient' => 'from-purple-900 via-blue-800 to-green-900']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $attributes = $__attributesOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $component = $__componentOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__componentOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>

<!-- Blog Posts Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl lg:text-5xl font-bold font-heading text-gray-900 mb-6">
                Latest <span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600">Articles</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Expert insights, industry trends, and practical tips to help you navigate the world of logistics and shipping.
            </p>
        </div>

        <?php if($posts->count() > 0): ?>
        <!-- Blog Posts Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <article class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden animate-on-scroll">
                <!-- Featured Image -->
                <div class="relative h-64 bg-gradient-to-br from-purple-500 to-blue-500 overflow-hidden">
                    <?php if($post->featured_image): ?>
                        <img src="<?php echo e(Storage::url($post->featured_image)); ?>" alt="<?php echo e($post->title); ?>" 
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                    <?php else: ?>
                        <div class="w-full h-full flex items-center justify-center">
                            <div class="text-center text-white">
                                <i class="fas fa-newspaper text-4xl mb-4 opacity-80"></i>
                                <p class="text-lg font-semibold opacity-90"><?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Date Badge -->
                    <div class="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                        <span class="text-sm font-semibold text-gray-800">
                            <?php echo e($post->published_at->format('M d, Y')); ?>

                        </span>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-8">
                    <div class="mb-4">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors line-clamp-2">
                            <?php echo e($post->title); ?>

                        </h3>
                        <p class="text-gray-600 leading-relaxed line-clamp-3">
                            <?php echo e($post->excerpt); ?>

                        </p>
                    </div>

                    <!-- Meta Info -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-user-circle mr-2 text-purple-500"></i>
                            <?php echo e($post->author->name ?? 'Admin'); ?>

                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2 text-blue-500"></i>
                            <?php echo e($post->published_at->diffForHumans()); ?>

                        </div>
                    </div>

                    <!-- Read More Button -->
                    <a href="<?php echo e(route('blog.show', $post->slug)); ?>" 
                       class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg font-semibold transition-all duration-300 group-hover:shadow-lg">
                        <span>Read More</span>
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </article>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Load More Button -->
        <div class="text-center mt-16 animate-on-scroll">
            <button class="inline-flex items-center px-8 py-4 bg-white hover:bg-gray-50 text-gray-800 border-2 border-gray-300 hover:border-purple-500 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                <i class="fas fa-plus mr-3"></i>
                Load More Articles
            </button>
        </div>
        <?php else: ?>
        <!-- No Posts State -->
        <div class="text-center py-20 animate-on-scroll">
            <div class="max-w-md mx-auto">
                <i class="fas fa-newspaper text-6xl text-gray-400 mb-6"></i>
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Coming Soon</h3>
                <p class="text-gray-600 mb-6">We're working on bringing you the latest logistics insights and industry news. Check back soon!</p>
                <a href="<?php echo e(route('contact')); ?>" class="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    Contact Us for Updates
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Newsletter Subscription -->
<section class="py-20 bg-gradient-to-br from-purple-900 via-blue-800 to-green-900 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-4xl mx-auto text-center animate-on-scroll">
            <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
                Stay <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400">Informed</span>
            </h2>
            <p class="text-xl text-gray-300 mb-8">
                Subscribe to our newsletter and never miss the latest logistics insights, industry trends, and expert tips.
            </p>

            <!-- Newsletter Form -->
            <form action="<?php echo e(route('newsletter.subscribe')); ?>" method="POST" class="max-w-md mx-auto">
                <?php echo csrf_field(); ?>
                <div class="flex gap-4">
                    <input type="email" name="email" required
                           class="flex-1 px-6 py-4 rounded-xl border-0 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300"
                           placeholder="Enter your email address">
                    <button type="submit"
                            class="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Subscribe
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/frontend/blog/index.blade.php ENDPATH**/ ?>