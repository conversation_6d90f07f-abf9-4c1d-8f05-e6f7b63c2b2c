@extends('layouts.admin')

@section('title', 'Blog Management')
@section('page-title', 'Blog Posts')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.blog.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Create Post
        </a>
        <button type="button" class="btn btn-outline-secondary" onclick="exportPosts()">
            <i class="fas fa-download me-1"></i> Export
        </button>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.blog.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Search posts...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                        <option value="published" {{ request('status') === 'published' ? 'selected' : '' }}>Published</option>
                        <option value="archived" {{ request('status') === 'archived' ? 'selected' : '' }}>Archived</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="author" class="form-label">Author</label>
                    <select class="form-select" id="author" name="author">
                        <option value="">All Authors</option>
                        @foreach($authors as $author)
                            <option value="{{ $author->id }}" {{ request('author') == $author->id ? 'selected' : '' }}>
                                {{ $author->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i> Filter
                        </button>
                        <a href="{{ route('admin.blog.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Blog Posts Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-blog me-2"></i>
                Blog Posts ({{ $posts->total() }})
            </h6>
        </div>
        <div class="card-body">
            @if($posts->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Author</th>
                                <th>Status</th>
                                <th>Published</th>
                                <th>Views</th>
                                <th>Featured</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($posts as $post)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($post->featured_image)
                                            <img src="{{ Storage::url($post->featured_image) }}" 
                                                 alt="{{ $post->title }}" class="rounded me-2" 
                                                 style="width: 40px; height: 40px; object-fit: cover;">
                                        @else
                                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                                 style="width: 40px; height: 40px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <h6 class="mb-0">{{ Str::limit($post->title, 50) }}</h6>
                                            <small class="text-muted">{{ Str::limit($post->excerpt, 80) }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ $post->author->name ?? 'Unknown' }}</td>
                                <td>
                                    @if($post->status === 'published')
                                        <span class="badge bg-success">Published</span>
                                    @elseif($post->status === 'draft')
                                        <span class="badge bg-warning">Draft</span>
                                    @else
                                        <span class="badge bg-secondary">Archived</span>
                                    @endif
                                </td>
                                <td>
                                    @if($post->published_at)
                                        {{ $post->published_at->format('M d, Y') }}
                                    @else
                                        <span class="text-muted">Not published</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ number_format($post->view_count) }}</span>
                                </td>
                                <td>
                                    <button type="button"
                                            class="btn btn-link p-0 featured-toggle"
                                            data-post-id="{{ $post->id }}"
                                            data-featured="{{ $post->is_featured ? 'true' : 'false' }}"
                                            title="{{ $post->is_featured ? 'Remove from featured' : 'Mark as featured' }}">
                                        <i class="fas fa-star {{ $post->is_featured ? 'text-warning' : 'text-muted' }}"></i>
                                    </button>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ route('admin.blog.show', $post) }}" 
                                           class="btn btn-outline-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.blog.edit', $post) }}" 
                                           class="btn btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deletePost({{ $post->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    {{ $posts->links('pagination.admin') }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-blog fa-4x text-muted mb-4"></i>
                    <h5 class="text-muted">No Blog Posts Found</h5>
                    <p class="text-muted">Get started by creating your first blog post.</p>
                    <a href="{{ route('admin.blog.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Create First Post
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Blog Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this blog post? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deletePost(postId) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/blog/${postId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function exportPosts() {
    // Implementation for exporting posts
    alert('Export functionality coming soon!');
}

// AJAX Featured Toggle
document.addEventListener('DOMContentLoaded', function() {
    const featuredToggles = document.querySelectorAll('.featured-toggle');

    featuredToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const postId = this.dataset.postId;
            const isFeatured = this.dataset.featured === 'true';
            const icon = this.querySelector('i');
            const button = this;

            // Disable button during request
            button.disabled = true;

            fetch(`/admin/blog/${postId}/toggle-featured`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update button state
                    button.dataset.featured = data.is_featured ? 'true' : 'false';

                    // Update icon
                    if (data.is_featured) {
                        icon.classList.remove('text-muted');
                        icon.classList.add('text-warning');
                        button.title = 'Remove from featured';
                    } else {
                        icon.classList.remove('text-warning');
                        icon.classList.add('text-muted');
                        button.title = 'Mark as featured';
                    }

                    // Show success alert
                    alert(data.message);
                } else {
                    alert(data.message || 'Failed to update featured status');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            })
            .finally(() => {
                // Re-enable button
                button.disabled = false;
            });
        });
    });
});
</script>
@endpush
