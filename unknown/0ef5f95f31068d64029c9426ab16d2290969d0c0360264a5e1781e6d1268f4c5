@extends('layouts.frontend')

@section('title', $post->title . ' - Blog - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics'))
@section('description', $post->excerpt)

@section('content')

<!-- Hero Section -->
<x-page-hero 
    title="{{ $post->title }}"
    subtitle="Published {{ $post->published_at->format('F d, Y') }}"
    description="{{ $post->excerpt }}"
    :breadcrumbs="[
        ['title' => 'Home', 'url' => route('home')],
        ['title' => 'Blog', 'url' => route('blog.index')],
        ['title' => $post->title]
    ]"
    gradient="from-purple-900 via-blue-800 to-green-900"
/>

<!-- Blog Post Content -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Article Header -->
            <header class="mb-12 animate-on-scroll">
                <div class="flex items-center justify-between text-sm text-gray-500 mb-6">
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center">
                            <i class="fas fa-user-circle mr-2 text-purple-500"></i>
                            <span>{{ $post->author->name ?? 'Admin' }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-calendar mr-2 text-blue-500"></i>
                            <span>{{ $post->published_at->format('F d, Y') }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2 text-green-500"></i>
                            <span>{{ $post->reading_time ?? 5 }} min read</span>
                        </div>
                    </div>
                    
                    <!-- Share Buttons -->
                    <div class="flex items-center space-x-3">
                        <span class="text-gray-600 font-medium">Share:</span>
                        <a href="#" onclick="shareOnFacebook()" class="text-blue-600 hover:text-blue-700 transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" onclick="shareOnTwitter()" class="text-blue-400 hover:text-blue-500 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" onclick="shareOnLinkedIn()" class="text-blue-700 hover:text-blue-800 transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <button onclick="copyToClipboard()" class="text-gray-600 hover:text-gray-700 transition-colors">
                            <i class="fas fa-link"></i>
                        </button>
                    </div>
                </div>

                @if($post->featured_image)
                <!-- Featured Image -->
                <div class="relative h-96 rounded-2xl overflow-hidden mb-8">
                    <img src="{{ Storage::url($post->featured_image) }}" alt="{{ $post->title }}" 
                         class="w-full h-full object-cover">
                </div>
                @endif
            </header>

            <!-- Article Content -->
            <article class="prose prose-lg max-w-none animate-on-scroll">
                <div class="text-gray-700 leading-relaxed">
                    @if($post->excerpt)
                        <p class="text-xl text-gray-600 mb-8 font-medium">{{ $post->excerpt }}</p>
                    @endif

                    <div class="space-y-6">
                        {!! nl2br(e($post->content)) !!}
                    </div>

                    <!-- Additional Image -->
                    @if($post->additional_image)
                        <div class="my-8 text-center">
                            <img src="{{ Storage::url($post->additional_image) }}"
                                 alt="{{ $post->title }}"
                                 class="w-full max-w-4xl mx-auto rounded-xl shadow-lg">
                        </div>
                    @endif
                </div>
            </article>

            <!-- Call to Action -->
            <div class="mt-16 p-8 bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl animate-on-scroll">
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready to Transform Your Logistics?</h3>
                    <p class="text-gray-600 mb-6">Let our experts help you implement cutting-edge logistics solutions for your business.</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button onclick="openQuoteModal()" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300">
                            Get a Quote
                        </button>
                        <a href="{{ route('contact') }}" class="bg-white hover:bg-gray-50 text-gray-800 border-2 border-gray-300 hover:border-purple-500 px-8 py-3 rounded-lg font-semibold transition-all duration-300">
                            Contact Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Articles -->
@if($relatedPosts && $relatedPosts->count() > 0)
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-3xl font-bold text-gray-900 text-center mb-12 animate-on-scroll">
                Related <span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600">Articles</span>
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                @foreach($relatedPosts->take(2) as $relatedPost)
                <article class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden animate-on-scroll">
                    <!-- Featured Image or Gradient Background -->
                    @if($relatedPost->featured_image)
                        <div class="h-48 overflow-hidden">
                            <img src="{{ Storage::url($relatedPost->featured_image) }}"
                                 alt="{{ $relatedPost->title }}"
                                 class="w-full h-full object-cover hover:scale-110 transition-transform duration-500">
                        </div>
                    @else
                        @php
                            $gradients = [
                                'from-blue-500 to-green-500',
                                'from-orange-500 to-red-500',
                                'from-purple-500 to-pink-500',
                                'from-indigo-500 to-blue-500',
                                'from-green-500 to-teal-500'
                            ];
                            $icons = ['fa-leaf', 'fa-chart-line', 'fa-shipping-fast', 'fa-globe', 'fa-cogs'];
                            $index = $loop->index % count($gradients);
                        @endphp
                        <div class="h-48 bg-gradient-to-br {{ $gradients[$index] }} flex items-center justify-center">
                            <div class="text-center text-white">
                                <i class="fas {{ $icons[$index] }} text-3xl mb-3 opacity-80"></i>
                                <p class="font-semibold opacity-90">{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</p>
                            </div>
                        </div>
                    @endif

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3">{{ Str::limit($relatedPost->title, 60) }}</h3>
                        <p class="text-gray-600 mb-4">{{ Str::limit($relatedPost->excerpt, 120) }}</p>

                        <!-- Post Meta -->
                        <div class="flex items-center text-sm text-gray-500 mb-4">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            <span>{{ $relatedPost->published_at->format('M d, Y') }}</span>
                            @if($relatedPost->reading_time)
                                <span class="mx-2">•</span>
                                <i class="fas fa-clock mr-1"></i>
                                <span>{{ $relatedPost->reading_time }} min read</span>
                            @endif
                        </div>

                        <a href="{{ route('blog.show', $relatedPost->slug) }}" class="text-blue-600 hover:text-blue-700 font-semibold">
                            Read More <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </article>
                @endforeach

                @if($relatedPosts->count() == 1)
                <!-- Fallback: Show a call-to-action card if only one related post -->
                <article class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden animate-on-scroll">
                    <div class="h-48 bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center">
                        <div class="text-center text-white">
                            <i class="fas fa-blog text-3xl mb-3 opacity-80"></i>
                            <p class="font-semibold opacity-90">{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</p>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Explore More Articles</h3>
                        <p class="text-gray-600 mb-4">Discover more insights and industry news on our blog.</p>
                        <a href="{{ route('blog.index') }}" class="text-blue-600 hover:text-blue-700 font-semibold">
                            View All Posts <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </article>
                @endif
            </div>
        </div>
    </div>
</section>
@endif

@endsection

@push('scripts')
<script>
function shareOnFacebook() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank', 'width=600,height=400');
}

function shareOnTwitter() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank', 'width=600,height=400');
}

function shareOnLinkedIn() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank', 'width=600,height=400');
}

function copyToClipboard() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-y-full transition-transform duration-300';
        toast.textContent = 'Link copied to clipboard!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.transform = 'translateY(0)';
        }, 100);
        
        setTimeout(() => {
            toast.style.transform = 'translateY(100%)';
            setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
    });
}
</script>
@endpush
