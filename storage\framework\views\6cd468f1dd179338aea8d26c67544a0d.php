<?php $__env->startSection('title', 'Blog Management'); ?>
<?php $__env->startSection('page-title', 'Blog Posts'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.blog.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Create Post
        </a>
        <button type="button" class="btn btn-outline-secondary" onclick="exportPosts()">
            <i class="fas fa-download me-1"></i> Export
        </button>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.blog.index')); ?>" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo e(request('search')); ?>" placeholder="Search posts...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="draft" <?php echo e(request('status') === 'draft' ? 'selected' : ''); ?>>Draft</option>
                        <option value="published" <?php echo e(request('status') === 'published' ? 'selected' : ''); ?>>Published</option>
                        <option value="archived" <?php echo e(request('status') === 'archived' ? 'selected' : ''); ?>>Archived</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="author" class="form-label">Author</label>
                    <select class="form-select" id="author" name="author">
                        <option value="">All Authors</option>
                        <?php $__currentLoopData = $authors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $author): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($author->id); ?>" <?php echo e(request('author') == $author->id ? 'selected' : ''); ?>>
                                <?php echo e($author->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i> Filter
                        </button>
                        <a href="<?php echo e(route('admin.blog.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Blog Posts Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-blog me-2"></i>
                Blog Posts (<?php echo e($posts->total()); ?>)
            </h6>
        </div>
        <div class="card-body">
            <?php if($posts->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Author</th>
                                <th>Status</th>
                                <th>Published</th>
                                <th>Views</th>
                                <th>Featured</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if($post->featured_image): ?>
                                            <img src="<?php echo e(Storage::url($post->featured_image)); ?>" 
                                                 alt="<?php echo e($post->title); ?>" class="rounded me-2" 
                                                 style="width: 40px; height: 40px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                                 style="width: 40px; height: 40px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-0"><?php echo e(Str::limit($post->title, 50)); ?></h6>
                                            <small class="text-muted"><?php echo e(Str::limit($post->excerpt, 80)); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo e($post->author->name ?? 'Unknown'); ?></td>
                                <td>
                                    <?php if($post->status === 'published'): ?>
                                        <span class="badge bg-success">Published</span>
                                    <?php elseif($post->status === 'draft'): ?>
                                        <span class="badge bg-warning">Draft</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Archived</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($post->published_at): ?>
                                        <?php echo e($post->published_at->format('M d, Y')); ?>

                                    <?php else: ?>
                                        <span class="text-muted">Not published</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo e(number_format($post->view_count)); ?></span>
                                </td>
                                <td>
                                    <?php if($post->is_featured): ?>
                                        <i class="fas fa-star text-warning" title="Featured"></i>
                                    <?php else: ?>
                                        <i class="far fa-star text-muted" title="Not featured"></i>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="<?php echo e(route('admin.blog.show', $post)); ?>" 
                                           class="btn btn-outline-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.blog.edit', $post)); ?>" 
                                           class="btn btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deletePost(<?php echo e($post->id); ?>)" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    <?php echo e($posts->links('pagination.admin')); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-blog fa-4x text-muted mb-4"></i>
                    <h5 class="text-muted">No Blog Posts Found</h5>
                    <p class="text-muted">Get started by creating your first blog post.</p>
                    <a href="<?php echo e(route('admin.blog.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Create First Post
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Blog Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this blog post? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function deletePost(postId) {
    const form = document.getElementById('deleteForm');
    form.action = `/admin/blog/${postId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function exportPosts() {
    // Implementation for exporting posts
    alert('Export functionality coming soon!');
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/blog/index.blade.php ENDPATH**/ ?>