<?php $__env->startSection('title', 'Blog Post: ' . $blogPost->title); ?>
<?php $__env->startSection('page-title', 'Blog Post Details'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.blog.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Posts
        </a>
        <a href="<?php echo e(route('admin.blog.edit', $blogPost)); ?>" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Post
        </a>
        <a href="<?php echo e(route('blog.show', $blogPost)); ?>" class="btn btn-outline-info" target="_blank">
            <i class="fas fa-external-link-alt me-1"></i> View Live
        </a>
        <button type="button" class="btn btn-outline-danger" onclick="deletePost()">
            <i class="fas fa-trash me-1"></i> Delete
        </button>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold text-primary"><?php echo e($blogPost->title); ?></h5>
                </div>
                <div class="card-body">
                    <!-- Featured Image -->
                    <?php if($blogPost->featured_image): ?>
                        <div class="mb-4 text-center">
                            <h6 class="text-muted mb-2">Featured Image</h6>
                            <img src="<?php echo e(Storage::url($blogPost->featured_image)); ?>"
                                 alt="<?php echo e($blogPost->title); ?>"
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 400px;">
                        </div>
                    <?php endif; ?>

                    <!-- Additional Image -->
                    <?php if($blogPost->additional_image): ?>
                        <div class="mb-4 text-center">
                            <h6 class="text-muted mb-2">Additional Image</h6>
                            <img src="<?php echo e(Storage::url($blogPost->additional_image)); ?>"
                                 alt="<?php echo e($blogPost->title); ?>"
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 400px;">
                        </div>
                    <?php endif; ?>

                    <!-- Excerpt -->
                    <?php if($blogPost->excerpt): ?>
                        <div class="alert alert-light border-start border-primary border-4 mb-4">
                            <h6 class="text-primary mb-2">Excerpt</h6>
                            <p class="mb-0"><?php echo e($blogPost->excerpt); ?></p>
                        </div>
                    <?php endif; ?>

                    <!-- Content -->
                    <div class="blog-content">
                        <?php echo nl2br(e($blogPost->content)); ?>

                    </div>

                    <!-- Tags -->
                    <?php if($blogPost->tags && count($blogPost->tags) > 0): ?>
                        <div class="mt-4">
                            <h6 class="text-muted mb-2">Tags</h6>
                            <?php $__currentLoopData = $blogPost->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="badge bg-secondary me-1"><?php echo e($tag); ?></span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- SEO Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-search me-2"></i>
                        SEO Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Meta Title</h6>
                            <p><?php echo e($blogPost->meta_title ?: $blogPost->title); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Meta Keywords</h6>
                            <p><?php echo e($blogPost->meta_keywords ?: 'Not set'); ?></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted">Meta Description</h6>
                            <p><?php echo e($blogPost->meta_description ?: $blogPost->excerpt ?: 'Not set'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Post Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Post Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Status</h6>
                            <?php if($blogPost->status === 'published'): ?>
                                <span class="badge bg-success">Published</span>
                            <?php elseif($blogPost->status === 'draft'): ?>
                                <span class="badge bg-warning">Draft</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Archived</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Featured</h6>
                            <?php if($blogPost->is_featured): ?>
                                <span class="badge bg-warning">
                                    <i class="fas fa-star me-1"></i>Featured
                                </span>
                            <?php else: ?>
                                <span class="text-muted">No</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Author</h6>
                            <p class="mb-0"><?php echo e($blogPost->author->name ?? 'Unknown'); ?></p>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Reading Time</h6>
                            <p class="mb-0"><?php echo e($blogPost->reading_time ?? 5); ?> min</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Views</h6>
                            <p class="mb-0"><?php echo e(number_format($blogPost->view_count)); ?></p>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Sort Order</h6>
                            <p class="mb-0"><?php echo e($blogPost->sort_order ?? 0); ?></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <h6 class="text-muted mb-1">Slug</h6>
                            <p class="mb-0 text-break"><?php echo e($blogPost->slug); ?></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Created</h6>
                            <p class="mb-0"><?php echo e($blogPost->created_at->format('M d, Y')); ?></p>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted mb-1">Updated</h6>
                            <p class="mb-0"><?php echo e($blogPost->updated_at->format('M d, Y')); ?></p>
                        </div>
                    </div>

                    <?php if($blogPost->published_at): ?>
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted mb-1">Published</h6>
                                <p class="mb-0"><?php echo e($blogPost->published_at->format('M d, Y g:i A')); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if($blogPost->status === 'draft'): ?>
                            <form method="POST" action="<?php echo e(route('admin.blog.update', $blogPost)); ?>" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PUT'); ?>
                                <input type="hidden" name="status" value="published">
                                <input type="hidden" name="title" value="<?php echo e($blogPost->title); ?>">
                                <input type="hidden" name="content" value="<?php echo e($blogPost->content); ?>">
                                <input type="hidden" name="author_id" value="<?php echo e($blogPost->author_id); ?>">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-globe me-1"></i> Publish Now
                                </button>
                            </form>
                        <?php elseif($blogPost->status === 'published'): ?>
                            <form method="POST" action="<?php echo e(route('admin.blog.update', $blogPost)); ?>" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PUT'); ?>
                                <input type="hidden" name="status" value="draft">
                                <input type="hidden" name="title" value="<?php echo e($blogPost->title); ?>">
                                <input type="hidden" name="content" value="<?php echo e($blogPost->content); ?>">
                                <input type="hidden" name="author_id" value="<?php echo e($blogPost->author_id); ?>">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-eye-slash me-1"></i> Unpublish
                                </button>
                            </form>
                        <?php endif; ?>

                        <button type="button" class="btn btn-outline-primary" onclick="duplicatePost()">
                            <i class="fas fa-copy me-1"></i> Duplicate Post
                        </button>

                        <a href="<?php echo e(route('admin.blog.create')); ?>" class="btn btn-outline-success">
                            <i class="fas fa-plus me-1"></i> Create New Post
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Blog Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this blog post? This action cannot be undone.</p>
                <p><strong><?php echo e($blogPost->title); ?></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="<?php echo e(route('admin.blog.destroy', $blogPost)); ?>" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.blog-content {
    line-height: 1.8;
    font-size: 1.1rem;
}

.blog-content p {
    margin-bottom: 1rem;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function deletePost() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function duplicatePost() {
    // Implementation for duplicating post
    alert('Duplicate functionality coming soon!');
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/blog/show.blade.php ENDPATH**/ ?>